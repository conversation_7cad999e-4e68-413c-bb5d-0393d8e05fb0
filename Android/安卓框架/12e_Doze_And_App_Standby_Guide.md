# Doze 和 App Standby 模式详解

## 概述

Doze 和 App Standby 是 Android 6.0 (API 23) 引入的电池优化功能，对应用保活产生重大影响。本文档详细说明这些机制的工作原理以及对各种保活技术的影响。

---

## 1. Doze 模式详解

### 1.1 Doze 模式触发条件

Doze 模式在以下条件同时满足时激活：

```kotlin
class DozeConditions {
    companion object {
        fun getDozeActivationConditions(): List<String> {
            return listOf(
                "📱 屏幕关闭",
                "🚫 设备静止不动（通过加速度计检测）",
                "🔋 设备未充电",
                "⏰ 一段时间无用户交互（通常30分钟-2小时）",
                "📍 设备位置相对固定"
            )
        }
        
        fun getDozeExitConditions(): List<String> {
            return listOf(
                "📱 用户唤醒设备",
                "🔌 设备开始充电",
                "📱 设备移动",
                "⏰ 维护窗口期到达",
                "📞 接收到高优先级推送消息"
            )
        }
    }
}
```

### 1.2 Doze 模式级别

#### Light Doze (Android 7.0+)
- **触发条件**: 屏幕关闭一段时间
- **限制程度**: 中等
- **网络访问**: 部分限制

#### Deep Doze (Android 6.0+)
- **触发条件**: 设备静止且屏幕关闭
- **限制程度**: 严格
- **网络访问**: 完全阻止

### 1.3 Doze 模式的影响

```kotlin
class DozeImpactAnalysis {
    
    data class DozeImpact(
        val feature: String,
        val lightDozeImpact: String,
        val deepDozeImpact: String,
        val workaround: String
    )
    
    companion object {
        fun getDozeImpactTable(): List<DozeImpact> {
            return listOf(
                DozeImpact(
                    feature = "网络访问",
                    lightDozeImpact = "延迟",
                    deepDozeImpact = "阻止",
                    workaround = "前台服务 / FCM高优先级消息"
                ),
                DozeImpact(
                    feature = "AlarmManager",
                    lightDozeImpact = "延迟到维护窗口",
                    deepDozeImpact = "延迟到维护窗口",
                    workaround = "setExactAndAllowWhileIdle / setAlarmClock"
                ),
                DozeImpact(
                    feature = "JobScheduler",
                    lightDozeImpact = "延迟",
                    deepDozeImpact = "延迟到维护窗口",
                    workaround = "前台服务"
                ),
                DozeImpact(
                    feature = "WorkManager",
                    lightDozeImpact = "延迟",
                    deepDozeImpact = "延迟到维护窗口",
                    workaround = "前台服务 / 约束条件"
                ),
                DozeImpact(
                    feature = "同步适配器",
                    lightDozeImpact = "停止",
                    deepDozeImpact = "停止",
                    workaround = "前台服务 / WorkManager"
                ),
                DozeImpact(
                    feature = "WiFi扫描",
                    lightDozeImpact = "限制",
                    deepDozeImpact = "停止",
                    workaround = "前台服务"
                ),
                DozeImpact(
                    feature = "GPS定位",
                    lightDozeImpact = "限制",
                    deepDozeImpact = "停止",
                    workaround = "前台服务"
                )
            )
        }
    }
}
```

---

## 2. App Standby 模式详解

### 2.1 App Standby 触发条件

```kotlin
class AppStandbyConditions {
    companion fun {
        fun getStandbyActivationConditions(): List<String> {
            return listOf(
                "👤 用户长时间未直接启动应用（通常3天）",
                "🚫 应用没有前台服务运行",
                "🔔 应用没有活跃的通知",
                "📱 应用不在最近任务列表中",
                "⚡ 应用不在电池优化白名单中",
                "🎯 应用没有活跃的设备管理员权限"
            )
        }
        
        fun getStandbyExitConditions(): List<String> {
            return listOf(
                "👤 用户主动启动应用",
                "🔔 应用显示通知",
                "🎯 应用启动前台服务",
                "🔌 设备充电时短暂恢复",
                "📞 应用接收到高优先级推送",
                "⚙️ 应用被系统或其他应用调用"
            )
        }
    }
}
```

### 2.2 App Standby 桶分类 (Android 9.0+)

Android 9.0 引入了应用桶分类系统：

```kotlin
class AppStandbyBuckets {
    
    enum class StandbyBucket(val level: Int, val description: String, val limitations: String) {
        ACTIVE(10, "活跃", "无限制"),
        WORKING_SET(20, "工作集", "轻微限制"),
        FREQUENT(30, "频繁使用", "中等限制"), 
        RARE(40, "很少使用", "严格限制"),
        RESTRICTED(50, "受限", "最严格限制")
    }
    
    companion object {
        fun getBucketLimitations(): Map<StandbyBucket, String> {
            return mapOf(
                StandbyBucket.ACTIVE to """
                    - 无限制
                    - 正常网络访问
                    - 正常后台任务执行
                """.trimIndent(),
                
                StandbyBucket.WORKING_SET to """
                    - 轻微限制
                    - JobScheduler延迟增加
                    - AlarmManager频率限制
                """.trimIndent(),
                
                StandbyBucket.FREQUENT to """
                    - 中等限制
                    - 网络访问受限
                    - 后台任务延迟执行
                """.trimIndent(),
                
                StandbyBucket.RARE to """
                    - 严格限制
                    - 网络访问严重受限
                    - 后台任务大幅延迟
                    - 每天只有几次执行机会
                """.trimIndent(),
                
                StandbyBucket.RESTRICTED to """
                    - 最严格限制
                    - 几乎无网络访问
                    - 后台任务基本停止
                    - 只在用户交互时恢复
                """.trimIndent()
            )
        }
    }
}
```

---

## 3. 对各种保活技术的具体影响

### 3.1 WorkManager 受影响分析

```kotlin
class WorkManagerDozeImpact {
    
    fun analyzeWorkManagerBehavior(): String {
        return """
        WorkManager 在 Doze/Standby 模式下的行为：
        
        ✅ 优势：
        - 自动处理 Doze 和 App Standby
        - 智能延迟和重试机制
        - 约束条件感知
        - 系统级优化
        
        ❌ 限制：
        - 在 Deep Doze 中延迟到维护窗口
        - App Standby 会影响执行频率
        - 网络约束在 Doze 中无法满足
        
        💡 最佳实践：
        - 使用合适的约束条件
        - 设置合理的退避策略
        - 结合前台服务使用
        - 避免过于频繁的任务
        """.trimIndent()
    }
    
    fun getOptimalWorkManagerConfig(): String {
        return """
        // 针对 Doze/Standby 优化的 WorkManager 配置
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .setRequiresBatteryNotLow(true)  // 避免低电量时执行
            .setRequiresCharging(false)      // 不强制要求充电
            .setRequiresDeviceIdle(false)    // 不要求设备空闲
            .build()
        
        val workRequest = PeriodicWorkRequestBuilder<KeepAliveWorker>(
            15, TimeUnit.MINUTES,  // 最小间隔15分钟
            5, TimeUnit.MINUTES    // 弹性间隔5分钟
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL,
                WorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .build()
        """.trimIndent()
    }
}
```

### 3.2 JobScheduler 受影响分析

```kotlin
class JobSchedulerDozeImpact {
    
    fun analyzeJobSchedulerBehavior(): String {
        return """
        JobScheduler 在 Doze/Standby 模式下的行为：
        
        ❌ 主要限制：
        - 在 Doze 模式下延迟到维护窗口
        - App Standby 影响执行频率
        - 网络约束无法在 Doze 中满足
        - 周期性任务间隔增加
        
        ✅ 部分豁免：
        - 前台服务可以正常调度
        - 充电时有更多执行机会
        - 用户交互后短暂恢复
        
        💡 应对策略：
        - 结合前台服务使用
        - 设置合理的约束条件
        - 利用维护窗口期
        - 避免过于频繁的任务
        """.trimIndent()
    }
    
    fun getDozeAwareJobConfig(): String {
        return """
        // 针对 Doze 模式优化的 JobScheduler 配置
        val jobInfo = JobInfo.Builder(jobId, componentName)
            .setMinimumLatency(30 * 1000)      // 最小延迟30秒
            .setOverrideDeadline(2 * 60 * 1000) // 最大延迟2分钟
            .setRequiredNetworkType(JobInfo.NETWORK_TYPE_NONE) // 不依赖网络
            .setRequiresCharging(false)         // 不要求充电
            .setRequiresDeviceIdle(false)       // 不要求设备空闲
            .setPersisted(true)                 // 重启后保持
            .build()
        """.trimIndent()
    }
}
```

### 3.3 AlarmManager 受影响分析

```kotlin
class AlarmManagerDozeImpact {
    
    fun analyzeAlarmManagerBehavior(): String {
        return """
        AlarmManager 在 Doze/Standby 模式下的行为：
        
        📱 普通闹钟 (setExact):
        - 在 Doze 模式下延迟到维护窗口
        - 维护窗口间隔逐渐增加（1小时 → 2小时 → 4小时...）
        - App Standby 会进一步延迟
        
        ⚡ setExactAndAllowWhileIdle:
        - 可以在 Doze 模式下执行
        - 频率限制：每个应用每15分钟最多1次
        - 不受 App Standby 影响
        
        🔔 setAlarmClock:
        - 在 Doze 模式下正常执行
        - 会在状态栏显示闹钟图标
        - 用户可见，不应频繁使用
        
        💡 使用建议：
        - 优先使用 setExactAndAllowWhileIdle
        - 遵守15分钟频率限制
        - 谨慎使用 setAlarmClock
        - 结合其他保活机制
        """.trimIndent()
    }
    
    fun getAlarmFrequencyLimits(): Map<String, String> {
        return mapOf(
            "setExact" to "延迟到维护窗口（1-4小时间隔）",
            "setExactAndAllowWhileIdle" to "每15分钟最多1次",
            "setAlarmClock" to "无频率限制，但影响用户体验",
            "setRepeating" to "已废弃，行为类似setExact"
        )
    }
}
```

---

## 4. 维护窗口期详解

### 4.1 维护窗口机制

```kotlin
class MaintenanceWindow {
    
    companion object {
        fun getMaintenanceWindowInfo(): String {
            return """
            维护窗口期 (Maintenance Window) 详解：
            
            ⏰ 触发时机：
            - 设备充电时
            - 用户主动使用设备时
            - 系统定期维护（间隔逐渐增加）
            
            📅 时间间隔：
            - 第1次：1小时后
            - 第2次：2小时后
            - 第3次：4小时后
            - 第4次：6小时后
            - 之后：每6小时一次
            
            🔄 窗口期行为：
            - 延迟的闹钟会执行
            - JobScheduler任务会运行
            - 网络访问临时恢复
            - 同步适配器可以工作
            
            ⚡ 充电时的特殊窗口：
            - 更频繁的维护窗口
            - 更长的执行时间
            - 更宽松的限制
            """.trimIndent()
        }
        
        fun getMaintenanceWindowStrategy(): String {
            return """
            利用维护窗口期的策略：
            
            1. 📊 批量处理：
               - 将多个任务合并到维护窗口期执行
               - 减少唤醒次数，提高效率
            
            2. 🔄 智能调度：
               - 监听充电状态
               - 在充电时执行重要任务
            
            3. 📱 用户交互感知：
               - 用户使用应用时立即执行待处理任务
               - 利用前台时间窗口
            
            4. 💾 本地缓存：
               - 在维护窗口期同步数据
               - 平时使用本地缓存
            """.trimIndent()
        }
    }
}
```

---

## 5. 白名单和豁免机制

### 5.1 电池优化白名单

```kotlin
class BatteryOptimizationWhitelist {
    
    fun checkWhitelistStatus(context: Context): WhitelistStatus {
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val isWhitelisted = powerManager.isIgnoringBatteryOptimizations(context.packageName)
            WhitelistStatus(
                isWhitelisted = isWhitelisted,
                canRequestWhitelist = true,
                benefits = getWhitelistBenefits()
            )
        } else {
            WhitelistStatus(
                isWhitelisted = true,
                canRequestWhitelist = false,
                benefits = emptyList()
            )
        }
    }
    
    private fun getWhitelistBenefits(): List<String> {
        return listOf(
            "🚫 不受 Doze 模式影响",
            "🚫 不受 App Standby 影响", 
            "⏰ AlarmManager 正常工作",
            "🌐 网络访问不受限制",
            "🔄 后台任务正常执行",
            "📊 JobScheduler 正常调度"
        )
    }
    
    fun requestWhitelistPermission(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                data = Uri.parse("package:${activity.packageName}")
            }
            
            try {
                activity.startActivity(intent)
            } catch (e: Exception) {
                // 如果无法直接跳转，引导用户手动设置
                val settingsIntent = Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
                activity.startActivity(settingsIntent)
            }
        }
    }
    
    data class WhitelistStatus(
        val isWhitelisted: Boolean,
        val canRequestWhitelist: Boolean,
        val benefits: List<String>
    )
}
```

### 5.2 其他豁免机制

```kotlin
class DozeExemptions {
    
    companion object {
        fun getExemptionMechanisms(): Map<String, String> {
            return mapOf(
                "前台服务" to "完全豁免 Doze 和 App Standby",
                "高优先级 FCM" to "可以唤醒应用并短暂豁免",
                "设备管理员" to "部分豁免（企业应用）",
                "系统应用" to "通常豁免大部分限制",
                "活跃通知" to "可以防止进入 App Standby",
                "最近使用" to "短期内豁免 App Standby",
                "充电状态" to "临时放宽限制",
                "用户交互" to "短暂恢复正常状态"
            )
        }
        
        fun getForegroundServiceBenefits(): List<String> {
            return listOf(
                "🔄 完全豁免 Doze 模式限制",
                "🔄 完全豁免 App Standby 限制",
                "🌐 正常网络访问",
                "⏰ AlarmManager 正常工作",
                "📊 JobScheduler 正常调度",
                "🔔 可以显示通知",
                "📱 保持应用活跃状态"
            )
        }
    }
}
```

---

## 6. 网络访问受限详解

### 6.1 网络受限的具体含义

在Doze和App Standby模式下，"网络访问受限"是指系统对应用的网络连接施加了各种限制：

#### 6.1.1 完全阻止网络访问 (Deep Doze模式)
```kotlin
// 在Deep Doze模式下的网络限制
class DeepDozeNetworkLimitations {
    companion object {
        fun getBlockedOperations(): List<String> {
            return listOf(
                "❌ HTTP/HTTPS请求被阻止",
                "❌ Socket连接被断开",
                "❌ WebSocket连接中断",
                "❌ 下载/上传任务停止",
                "❌ 实时通信中断",
                "❌ API调用失败",
                "❌ 数据同步停止"
            )
        }
    }
}
```

#### 6.1.2 延迟网络访问 (Light Doze/App Standby)
```kotlin
// 网络请求会被延迟到维护窗口期
class DelayedNetworkAccess {
    companion object {
        fun getDelayedOperations(): List<String> {
            return listOf(
                "⏳ API调用延迟1-6小时",
                "⏳ 数据同步推迟执行",
                "⏳ 图片/文件下载暂停",
                "⏳ 推送消息接收延迟",
                "⏳ 实时更新停止",
                "⏳ 后台上传暂停"
            )
        }
    }
}
```

#### 6.1.3 频率限制 (App Standby桶分类)
```kotlin
class NetworkAccessLimitations {

    fun getNetworkLimitsByBucket(): Map<String, String> {
        return mapOf(
            "ACTIVE" to "无限制 - 正常网络访问",
            "WORKING_SET" to "轻微限制 - 偶尔延迟",
            "FREQUENT" to "中等限制 - 明显延迟，批量处理",
            "RARE" to "严格限制 - 每天只有几次网络访问机会",
            "RESTRICTED" to "几乎无网络 - 只在用户交互时短暂恢复"
        )
    }

    fun getNetworkFailureReasons(): List<String> {
        return listOf(
            "ConnectException: Connection refused",
            "SocketTimeoutException: timeout",
            "UnknownHostException: Unable to resolve host",
            "IOException: Network is unreachable",
            "SecurityException: Network access denied"
        )
    }
}
```

### 6.2 网络受限的实际表现

#### 6.2.1 HTTP请求失败示例
```kotlin
class NetworkExample {

    fun makeHttpRequest() {
        try {
            val url = URL("https://api.example.com/data")
            val connection = url.openConnection()
            connection.connectTimeout = 5000
            connection.readTimeout = 10000

            // 在Doze模式下，这里会抛出异常
            val response = connection.getInputStream()
            val result = response.bufferedReader().use { it.readText() }

            Log.d("Network", "请求成功: $result")

        } catch (e: IOException) {
            // 网络被阻止时的异常处理
            Log.e("Network", "网络访问被阻止: ${e.message}")

            when (e) {
                is ConnectException -> {
                    Log.e("Network", "连接被拒绝 - 可能是Doze模式")
                    handleDozeNetworkBlock()
                }
                is SocketTimeoutException -> {
                    Log.e("Network", "连接超时 - 网络访问受限")
                    handleNetworkTimeout()
                }
                is UnknownHostException -> {
                    Log.e("Network", "无法解析主机 - DNS查询被阻止")
                    handleDnsBlock()
                }
                else -> {
                    Log.e("Network", "其他网络错误: ${e.javaClass.simpleName}")
                    handleGeneralNetworkError()
                }
            }
        }
    }

    private fun handleDozeNetworkBlock() {
        // 处理Doze模式网络阻止
        // 1. 将请求加入队列
        addToNetworkQueue()

        // 2. 尝试启动前台服务
        startForegroundServiceForNetwork()

        // 3. 通知用户网络受限
        showNetworkLimitedNotification()
    }

    private fun handleNetworkTimeout() {
        // 处理网络超时
        // 增加重试机制
        scheduleRetryWithBackoff()
    }

    private fun handleDnsBlock() {
        // 处理DNS查询被阻止
        // 使用本地缓存或离线模式
        switchToOfflineMode()
    }

    private fun handleGeneralNetworkError() {
        // 处理一般网络错误
        // 记录错误并稍后重试
        logErrorAndScheduleRetry()
    }
}
```

#### 6.2.2 WebSocket连接中断示例
```kotlin
class WebSocketExample {

    private var webSocket: WebSocket? = null
    private var isDozeMode = false

    fun setupWebSocket() {
        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()

        val request = Request.Builder()
            .url("wss://api.example.com/websocket")
            .build()

        webSocket = client.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d("WebSocket", "连接已建立")
                isDozeMode = false

                // 发送心跳包
                startHeartbeat(webSocket)
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                Log.d("WebSocket", "收到消息: $text")
                handleWebSocketMessage(text)
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e("WebSocket", "连接失败: ${t.message}")

                // 判断是否是Doze模式导致的断开
                if (isLikelyDozeDisconnection(t)) {
                    isDozeMode = true
                    handleDozeDisconnection()
                } else {
                    handleNormalDisconnection()
                }
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Log.d("WebSocket", "连接已关闭: $reason")

                if (!isDozeMode) {
                    // 非Doze模式断开，尝试重连
                    scheduleReconnection()
                }
            }
        })
    }

    private fun isLikelyDozeDisconnection(throwable: Throwable): Boolean {
        // 判断是否可能是Doze模式导致的断开
        return when (throwable) {
            is SocketTimeoutException -> true
            is ConnectException -> true
            is IOException -> throwable.message?.contains("Network is unreachable") == true
            else -> false
        }
    }

    private fun handleDozeDisconnection() {
        Log.w("WebSocket", "检测到Doze模式断开连接")

        // 1. 停止心跳
        stopHeartbeat()

        // 2. 启动前台服务维持连接
        startForegroundServiceForWebSocket()

        // 3. 等待维护窗口期重连
        scheduleMaintenanceWindowReconnection()
    }

    private fun handleNormalDisconnection() {
        Log.w("WebSocket", "正常网络断开，尝试重连")
        scheduleReconnection()
    }

    private fun startHeartbeat(webSocket: WebSocket) {
        // 发送心跳包保持连接
        Thread {
            while (webSocket != null && !isDozeMode) {
                try {
                    webSocket.send("ping")
                    Thread.sleep(30000) // 30秒心跳
                } catch (e: Exception) {
                    Log.e("WebSocket", "心跳发送失败", e)
                    break
                }
            }
        }.start()
    }
}
```

### 6.3 豁免网络限制的方法

#### 6.3.1 前台服务 (最有效的方法)
```kotlin
class NetworkForegroundService : Service() {

    private val networkHandler = Handler(Looper.getMainLooper())
    private var networkRunnable: Runnable? = null

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())

        Log.d("NetworkService", "前台服务已启动 - 网络访问不受Doze限制")

        // 前台服务中的网络访问不受Doze限制
        startNetworkOperations()
    }

    private fun startNetworkOperations() {
        networkRunnable = object : Runnable {
            override fun run() {
                try {
                    // 这些网络操作不会被Doze阻止
                    performNetworkTasks()

                    // 30秒后再次执行
                    networkHandler.postDelayed(this, 30000)

                } catch (e: Exception) {
                    Log.e("NetworkService", "网络操作失败", e)

                    // 出错时延长间隔
                    networkHandler.postDelayed(this, 60000)
                }
            }
        }

        // 立即开始第一次执行
        networkHandler.post(networkRunnable!!)
    }

    private fun performNetworkTasks() {
        Log.d("NetworkService", "执行网络任务 - 前台服务保护")

        // 1. 发送心跳包
        sendHeartbeat()

        // 2. 同步数据
        syncData()

        // 3. 下载更新
        downloadUpdates()

        // 4. 上传日志
        uploadLogs()

        Log.d("NetworkService", "网络任务执行完成")
    }

    private fun sendHeartbeat() {
        try {
            val url = URL("https://api.example.com/heartbeat")
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.connectTimeout = 5000
            connection.readTimeout = 10000

            val responseCode = connection.responseCode
            Log.d("NetworkService", "心跳响应: $responseCode")

        } catch (e: Exception) {
            Log.e("NetworkService", "心跳发送失败", e)
        }
    }

    private fun syncData() {
        // 数据同步逻辑
        Log.d("NetworkService", "执行数据同步")
    }

    private fun downloadUpdates() {
        // 下载更新逻辑
        Log.d("NetworkService", "下载更新")
    }

    private fun uploadLogs() {
        // 上传日志逻辑
        Log.d("NetworkService", "上传日志")
    }

    override fun onDestroy() {
        super.onDestroy()

        // 停止网络操作
        networkRunnable?.let { networkHandler.removeCallbacks(it) }

        Log.d("NetworkService", "前台服务已停止")
    }

    override fun onBind(intent: Intent?): IBinder? = null

    companion object {
        private const val NOTIFICATION_ID = 2001
        private const val CHANNEL_ID = "network_service_channel"
    }
}
```

#### 6.3.2 高优先级FCM推送
```kotlin
class FCMNetworkService : FirebaseMessagingService() {

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)

        Log.d("FCM", "收到FCM消息: ${remoteMessage.data}")

        // 高优先级FCM消息可以临时唤醒网络访问
        if (remoteMessage.priority == RemoteMessage.PRIORITY_HIGH) {
            Log.d("FCM", "高优先级消息 - 临时唤醒网络访问")

            // 在这个短暂窗口期内，网络访问是允许的
            performUrgentNetworkTask()
        } else {
            Log.d("FCM", "普通优先级消息 - 可能受网络限制影响")
            handleNormalPriorityMessage(remoteMessage)
        }
    }

    private fun performUrgentNetworkTask() {
        // 利用FCM唤醒的短暂窗口执行网络任务
        Thread {
            try {
                Log.d("FCM", "开始执行紧急网络任务")

                // 快速执行关键的网络操作（通常有10-15秒窗口）
                val urgentData = fetchUrgentData()
                processUrgentData(urgentData)

                // 同时执行其他待处理的网络任务
                executePendingNetworkTasks()

                Log.d("FCM", "紧急网络任务执行完成")

            } catch (e: Exception) {
                Log.e("FCM", "紧急网络任务失败", e)
            }
        }.start()
    }

    private fun fetchUrgentData(): String {
        val url = URL("https://api.example.com/urgent")
        val connection = url.openConnection() as HttpURLConnection
        connection.connectTimeout = 3000 // 短超时时间
        connection.readTimeout = 5000

        return connection.inputStream.bufferedReader().use { it.readText() }
    }

    private fun processUrgentData(data: String) {
        // 处理紧急数据
        Log.d("FCM", "处理紧急数据: $data")
    }

    private fun executePendingNetworkTasks() {
        // 执行队列中的待处理网络任务
        NetworkTaskQueue.getInstance().executePendingTasks()
    }

    private fun handleNormalPriorityMessage(remoteMessage: RemoteMessage) {
        // 处理普通优先级消息
        // 可能需要等待网络访问恢复
        val networkTask = NetworkTask {
            processNormalMessage(remoteMessage)
        }

        NetworkTaskQueue.getInstance().addTask(networkTask)
    }

    private fun processNormalMessage(remoteMessage: RemoteMessage) {
        // 处理普通消息的网络操作
        Log.d("FCM", "处理普通消息")
    }
}
```

### 6.4 应对网络受限的策略

#### 6.4.1 网络状态检测
```kotlin
class NetworkStateDetector(private val context: Context) {

    fun isNetworkAccessible(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.isConnected == true
        }
    }

    fun getNetworkAccessStatus(): String {
        val dozeDetector = DozeDetector(context)

        return when {
            dozeDetector.isDeviceInDozeMode() -> "🚫 Doze模式 - 网络被阻止"
            dozeDetector.isAppInStandbyMode() -> "⏳ Standby模式 - 网络受限"
            dozeDetector.isIgnoringBatteryOptimizations() -> "✅ 白名单 - 网络正常"
            isNetworkAccessible() -> "✅ 网络可用"
            else -> "❌ 网络不可用"
        }
    }

    fun isInMaintenanceWindow(): Boolean {
        // 检测是否在维护窗口期
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 如果设备不在Doze模式，可能是维护窗口期
            !powerManager.isDeviceIdleMode && isNetworkAccessible()
        } else {
            true
        }
    }

    fun getDetailedNetworkStatus(): NetworkStatus {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val dozeDetector = DozeDetector(context)

        return NetworkStatus(
            isConnected = isNetworkAccessible(),
            isDozeMode = dozeDetector.isDeviceInDozeMode(),
            isStandbyMode = dozeDetector.isAppInStandbyMode(),
            isWhitelisted = dozeDetector.isIgnoringBatteryOptimizations(),
            isMaintenanceWindow = isInMaintenanceWindow(),
            networkType = getNetworkType(connectivityManager)
        )
    }

    private fun getNetworkType(connectivityManager: ConnectivityManager): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)

            when {
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> "WiFi"
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> "移动网络"
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) == true -> "以太网"
                else -> "未知"
            }
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.typeName ?: "未知"
        }
    }

    data class NetworkStatus(
        val isConnected: Boolean,
        val isDozeMode: Boolean,
        val isStandbyMode: Boolean,
        val isWhitelisted: Boolean,
        val isMaintenanceWindow: Boolean,
        val networkType: String
    ) {
        fun getStatusDescription(): String {
            return buildString {
                appendLine("网络状态详情:")
                appendLine("- 网络连接: ${if (isConnected) "✅ 已连接" else "❌ 未连接"}")
                appendLine("- 网络类型: $networkType")
                appendLine("- Doze模式: ${if (isDozeMode) "🚫 是" else "✅ 否"}")
                appendLine("- Standby模式: ${if (isStandbyMode) "⏳ 是" else "✅ 否"}")
                appendLine("- 电池白名单: ${if (isWhitelisted) "✅ 已加入" else "❌ 未加入"}")
                appendLine("- 维护窗口: ${if (isMaintenanceWindow) "✅ 是" else "❌ 否"}")

                append("综合状态: ")
                append(when {
                    isWhitelisted -> "✅ 网络访问无限制"
                    isDozeMode -> "🚫 网络访问被阻止"
                    isStandbyMode -> "⏳ 网络访问受限"
                    isMaintenanceWindow -> "🔄 维护窗口期 - 网络临时可用"
                    isConnected -> "✅ 网络正常可用"
                    else -> "❌ 网络不可用"
                })
            }
        }
    }
}
```

#### 6.4.2 网络任务队列管理
```kotlin
class NetworkTaskQueue private constructor() {

    private val pendingTasks = mutableListOf<NetworkTask>()
    private val executedTasks = mutableListOf<NetworkTask>()
    private val failedTasks = mutableListOf<Pair<NetworkTask, Exception>>()

    companion object {
        @Volatile
        private var INSTANCE: NetworkTaskQueue? = null

        fun getInstance(): NetworkTaskQueue {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NetworkTaskQueue().also { INSTANCE = it }
            }
        }
    }

    fun addTask(task: NetworkTask) {
        synchronized(pendingTasks) {
            if (isNetworkAccessible()) {
                // 网络可用时立即执行
                executeTaskSafely(task)
            } else {
                // 网络受限时加入队列
                pendingTasks.add(task)
                Log.d("NetworkQueue", "网络受限，任务已加入队列。当前队列大小: ${pendingTasks.size}")
            }
        }
    }

    fun executePendingTasks() {
        synchronized(pendingTasks) {
            if (isNetworkAccessible() && pendingTasks.isNotEmpty()) {
                Log.d("NetworkQueue", "网络恢复，执行${pendingTasks.size}个待处理的网络任务")

                val tasksToExecute = pendingTasks.toList()
                pendingTasks.clear()

                tasksToExecute.forEach { task ->
                    executeTaskSafely(task)
                }

                Log.d("NetworkQueue", "待处理任务执行完成")
            }
        }
    }

    fun executePriorityTasks() {
        synchronized(pendingTasks) {
            // 执行高优先级任务
            val priorityTasks = pendingTasks.filter { it.priority == TaskPriority.HIGH }

            if (priorityTasks.isNotEmpty()) {
                Log.d("NetworkQueue", "执行${priorityTasks.size}个高优先级任务")

                priorityTasks.forEach { task ->
                    executeTaskSafely(task)
                    pendingTasks.remove(task)
                }
            }
        }
    }

    private fun executeTaskSafely(task: NetworkTask) {
        try {
            Log.d("NetworkQueue", "执行网络任务: ${task.name}")
            task.execute()

            synchronized(executedTasks) {
                executedTasks.add(task)
            }

            Log.d("NetworkQueue", "任务执行成功: ${task.name}")

        } catch (e: Exception) {
            Log.e("NetworkQueue", "任务执行失败: ${task.name}", e)

            synchronized(failedTasks) {
                failedTasks.add(Pair(task, e))
            }

            // 根据任务重试策略决定是否重新加入队列
            if (task.shouldRetry(e)) {
                synchronized(pendingTasks) {
                    pendingTasks.add(task)
                }
                Log.d("NetworkQueue", "任务将重试: ${task.name}")
            }
        }
    }

    fun getQueueStatus(): QueueStatus {
        return QueueStatus(
            pendingCount = pendingTasks.size,
            executedCount = executedTasks.size,
            failedCount = failedTasks.size,
            isNetworkAccessible = isNetworkAccessible()
        )
    }

    fun clearQueue() {
        synchronized(pendingTasks) {
            pendingTasks.clear()
        }
        synchronized(executedTasks) {
            executedTasks.clear()
        }
        synchronized(failedTasks) {
            failedTasks.clear()
        }
        Log.d("NetworkQueue", "队列已清空")
    }

    private fun isNetworkAccessible(): Boolean {
        // 这里应该注入Context，简化示例
        return true // 实际实现需要检查网络状态
    }

    data class QueueStatus(
        val pendingCount: Int,
        val executedCount: Int,
        val failedCount: Int,
        val isNetworkAccessible: Boolean
    ) {
        fun getStatusDescription(): String {
            return "队列状态 - 待处理: $pendingCount, 已执行: $executedCount, 失败: $failedCount, 网络: ${if (isNetworkAccessible) "可用" else "受限"}"
        }
    }
}

interface NetworkTask {
    val name: String
    val priority: TaskPriority

    fun execute()
    fun shouldRetry(exception: Exception): Boolean = false
}

enum class TaskPriority {
    LOW, NORMAL, HIGH, URGENT
}

// 具体的网络任务实现示例
class HeartbeatTask : NetworkTask {
    override val name = "心跳包"
    override val priority = TaskPriority.HIGH

    override fun execute() {
        // 发送心跳包
        val url = URL("https://api.example.com/heartbeat")
        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = "POST"
        connection.connectTimeout = 5000
        connection.readTimeout = 10000

        val responseCode = connection.responseCode
        Log.d("HeartbeatTask", "心跳响应: $responseCode")
    }

    override fun shouldRetry(exception: Exception): Boolean {
        // 心跳任务失败时重试
        return exception is IOException
    }
}

class DataSyncTask(private val data: String) : NetworkTask {
    override val name = "数据同步"
    override val priority = TaskPriority.NORMAL

    override fun execute() {
        // 同步数据
        val url = URL("https://api.example.com/sync")
        val connection = url.openConnection() as HttpURLConnection
        connection.requestMethod = "POST"
        connection.doOutput = true

        connection.outputStream.use { output ->
            output.write(data.toByteArray())
        }

        val responseCode = connection.responseCode
        Log.d("DataSyncTask", "同步响应: $responseCode")
    }

    override fun shouldRetry(exception: Exception): Boolean {
        // 数据同步失败时重试
        return exception is SocketTimeoutException || exception is ConnectException
    }
}
```

#### 6.4.3 批量网络操作策略
```kotlin
class BatchNetworkOperations(private val context: Context) {

    private val networkDetector = NetworkStateDetector(context)
    private val taskQueue = NetworkTaskQueue.getInstance()

    fun performBatchOperations() {
        // 在维护窗口期或网络恢复时批量执行网络操作
        Thread {
            try {
                val networkStatus = networkDetector.getDetailedNetworkStatus()
                Log.d("BatchNetwork", networkStatus.getStatusDescription())

                if (networkStatus.isConnected && !networkStatus.isDozeMode) {
                    Log.d("BatchNetwork", "开始批量网络操作")

                    // 1. 首先执行高优先级任务
                    taskQueue.executePriorityTasks()

                    // 2. 批量上传数据
                    batchUploadData()

                    // 3. 批量下载更新
                    batchDownloadUpdates()

                    // 4. 批量同步状态
                    batchSyncStatus()

                    // 5. 执行队列中的其他任务
                    taskQueue.executePendingTasks()

                    Log.d("BatchNetwork", "批量网络操作完成")

                } else {
                    Log.w("BatchNetwork", "网络不可用或处于Doze模式，跳过批量操作")
                }

            } catch (e: Exception) {
                Log.e("BatchNetwork", "批量操作失败", e)
            }
        }.start()
    }

    private fun batchUploadData() {
        Log.d("BatchNetwork", "开始批量上传数据")

        try {
            // 收集所有待上传的数据
            val pendingData = collectPendingUploadData()

            if (pendingData.isNotEmpty()) {
                // 将多个小的上传请求合并为一个大请求
                val batchData = combinePendingData(pendingData)
                uploadDataBatch(batchData)

                // 清理已上传的数据
                clearUploadedData(pendingData)
            }

        } catch (e: Exception) {
            Log.e("BatchNetwork", "批量上传失败", e)
        }
    }

    private fun batchDownloadUpdates() {
        Log.d("BatchNetwork", "开始批量下载更新")

        try {
            // 获取更新列表
            val updateList = getUpdateList()

            if (updateList.isNotEmpty()) {
                // 批量下载所有待更新的内容
                downloadUpdates(updateList)

                // 应用更新
                applyUpdates(updateList)
            }

        } catch (e: Exception) {
            Log.e("BatchNetwork", "批量下载失败", e)
        }
    }

    private fun batchSyncStatus() {
        Log.d("BatchNetwork", "开始批量同步状态")

        try {
            // 收集所有状态变更
            val statusChanges = collectStatusChanges()

            if (statusChanges.isNotEmpty()) {
                // 批量同步应用状态
                syncApplicationStatus(statusChanges)

                // 清理已同步的状态
                clearSyncedStatus(statusChanges)
            }

        } catch (e: Exception) {
            Log.e("BatchNetwork", "批量同步失败", e)
        }
    }

    private fun collectPendingUploadData(): List<UploadData> {
        // 从本地数据库或缓存中收集待上传的数据
        return listOf(
            UploadData("user_action", "click_button"),
            UploadData("app_event", "screen_view"),
            UploadData("error_log", "network_timeout")
        )
    }

    private fun combinePendingData(dataList: List<UploadData>): String {
        // 将多个数据项合并为一个JSON数组
        return dataList.joinToString(
            prefix = "[",
            postfix = "]",
            separator = ","
        ) { it.toJson() }
    }

    private fun uploadDataBatch(batchData: String) {
        val url = URL("https://api.example.com/batch_upload")
        val connection = url.openConnection() as HttpURLConnection

        connection.requestMethod = "POST"
        connection.setRequestProperty("Content-Type", "application/json")
        connection.doOutput = true

        connection.outputStream.use { output ->
            output.write(batchData.toByteArray())
        }

        val responseCode = connection.responseCode
        Log.d("BatchNetwork", "批量上传响应: $responseCode")
    }

    private fun clearUploadedData(uploadedData: List<UploadData>) {
        // 清理已成功上传的数据
        Log.d("BatchNetwork", "清理${uploadedData.size}条已上传数据")
    }

    private fun getUpdateList(): List<UpdateItem> {
        // 获取需要更新的项目列表
        return listOf(
            UpdateItem("config", "v1.2.0"),
            UpdateItem("assets", "v2.1.0")
        )
    }

    private fun downloadUpdates(updateList: List<UpdateItem>) {
        updateList.forEach { update ->
            try {
                downloadUpdate(update)
            } catch (e: Exception) {
                Log.e("BatchNetwork", "下载更新失败: ${update.name}", e)
            }
        }
    }

    private fun downloadUpdate(update: UpdateItem) {
        val url = URL("https://api.example.com/updates/${update.name}/${update.version}")
        val connection = url.openConnection()

        // 下载更新内容
        connection.inputStream.use { input ->
            // 保存到本地文件
            Log.d("BatchNetwork", "下载更新: ${update.name} ${update.version}")
        }
    }

    private fun applyUpdates(updateList: List<UpdateItem>) {
        // 应用下载的更新
        Log.d("BatchNetwork", "应用${updateList.size}个更新")
    }

    private fun collectStatusChanges(): List<StatusChange> {
        // 收集状态变更
        return listOf(
            StatusChange("app_version", "1.2.3"),
            StatusChange("last_active", System.currentTimeMillis().toString())
        )
    }

    private fun syncApplicationStatus(statusChanges: List<StatusChange>) {
        val url = URL("https://api.example.com/sync_status")
        val connection = url.openConnection() as HttpURLConnection

        connection.requestMethod = "POST"
        connection.setRequestProperty("Content-Type", "application/json")
        connection.doOutput = true

        val statusJson = statusChanges.joinToString(
            prefix = "{",
            postfix = "}",
            separator = ","
        ) { "\"${it.key}\":\"${it.value}\"" }

        connection.outputStream.use { output ->
            output.write(statusJson.toByteArray())
        }

        val responseCode = connection.responseCode
        Log.d("BatchNetwork", "状态同步响应: $responseCode")
    }

    private fun clearSyncedStatus(statusChanges: List<StatusChange>) {
        // 清理已同步的状态
        Log.d("BatchNetwork", "清理${statusChanges.size}条已同步状态")
    }

    data class UploadData(val type: String, val content: String) {
        fun toJson(): String = "{\"type\":\"$type\",\"content\":\"$content\"}"
    }

    data class UpdateItem(val name: String, val version: String)

    data class StatusChange(val key: String, val value: String)
}
```

---

## 7. 最佳实践总结

### 6.1 综合应对策略

1. **优先使用前台服务**: 最可靠的豁免机制
2. **申请电池优化白名单**: 用户同意后完全豁免
3. **合理使用 setExactAndAllowWhileIdle**: 遵守频率限制
4. **利用维护窗口期**: 批量处理任务
5. **监听充电状态**: 充电时执行重要任务
6. **用户交互感知**: 前台时立即处理待办任务

### 6.2 注意事项

1. **不要过度保活**: 影响用户体验和设备性能
2. **遵守系统限制**: 不要试图绕过 Doze 和 App Standby
3. **合理使用资源**: 在维护窗口期高效执行任务
4. **用户透明**: 让用户了解保活的必要性
5. **版本适配**: 不同 Android 版本行为可能不同

通过理解和正确应对 Doze 和 App Standby 机制，可以在保证用户体验的前提下实现有效的应用保活。
