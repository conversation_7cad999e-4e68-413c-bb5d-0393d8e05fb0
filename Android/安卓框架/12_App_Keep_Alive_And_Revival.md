# 12 - Android应用保活和拉活技术详解

## 概述

应用保活（Keep Alive）和拉活（Revival）是Android开发中的重要技术，用于确保应用在后台能够持续运行或在被杀死后能够重新启动。随着Android系统版本的不断更新，Google对后台应用的限制越来越严格，传统的保活方法逐渐失效，需要采用更加规范和系统友好的方式。

---

## 1. 保活技术分类

### 1.1 系统级保活
- **前台服务 (Foreground Service)**
- **JobScheduler / JobIntentService**
- **WorkManager (推荐)**
- **AlarmManager**

### 1.2 应用级保活
- **双进程守护**
- **Service自启动**
- **广播拉活**
- **账户同步拉活**

### 1.3 厂商级保活
- **白名单机制**
- **自启动管理**
- **后台应用保护**

---

## 2. WorkManager - 现代化后台任务解决方案

WorkManager是Google推荐的后台任务处理框架，能够在各种系统约束下可靠地执行任务。

### 2.1 WorkManager基本使用

#### 添加依赖
```gradle
dependencies {
    implementation "androidx.work:work-runtime:2.8.1"
    // 可选：RxJava2支持
    implementation "androidx.work:work-rxjava2:2.8.1"
    // 可选：Kotlin协程支持
    implementation "androidx.work:work-runtime-ktx:2.8.1"
}
```

#### 创建Worker
```kotlin
class KeepAliveWorker(context: Context, params: WorkerParameters) : Worker(context, params) {
    
    override fun doWork(): Result {
        return try {
            // 执行保活逻辑
            performKeepAliveTask()
            
            // 发送心跳包
            sendHeartbeat()
            
            // 检查应用状态
            checkAppStatus()
            
            Result.success()
        } catch (e: Exception) {
            Log.e("KeepAliveWorker", "保活任务执行失败", e)
            Result.retry()
        }
    }
    
    private fun performKeepAliveTask() {
        // 实现具体的保活逻辑
        Log.d("KeepAliveWorker", "执行保活任务: ${System.currentTimeMillis()}")
        
        // 可以在这里执行：
        // 1. 网络请求保持连接
        // 2. 数据同步
        // 3. 状态上报
        // 4. 缓存清理等
    }
    
    private fun sendHeartbeat() {
        // 发送心跳包到服务器
        // 实现网络保活逻辑
    }
    
    private fun checkAppStatus() {
        // 检查应用各模块状态
        // 必要时重新初始化
    }
}
```

#### 配置和启动WorkManager
```kotlin
class KeepAliveManager {
    
    companion object {
        private const val KEEP_ALIVE_WORK_NAME = "keep_alive_work"
        private const val PERIODIC_INTERVAL_MINUTES = 15L
    }
    
    fun startKeepAliveWork(context: Context) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED) // 需要网络连接
            .setRequiresBatteryNotLow(true) // 电量不能过低
            .setRequiresCharging(false) // 不要求充电状态
            .setRequiresDeviceIdle(false) // 不要求设备空闲
            .build()
        
        val keepAliveRequest = PeriodicWorkRequestBuilder<KeepAliveWorker>(
            PERIODIC_INTERVAL_MINUTES, TimeUnit.MINUTES
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.LINEAR,
                PeriodicWorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .build()
        
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            KEEP_ALIVE_WORK_NAME,
            ExistingPeriodicWorkPolicy.KEEP,
            keepAliveRequest
        )
    }
    
    fun stopKeepAliveWork(context: Context) {
        WorkManager.getInstance(context).cancelUniqueWork(KEEP_ALIVE_WORK_NAME)
    }
    
    fun getWorkStatus(context: Context): LiveData<List<WorkInfo>> {
        return WorkManager.getInstance(context).getWorkInfosForUniqueWorkLiveData(KEEP_ALIVE_WORK_NAME)
    }
}
```

### 2.2 WorkManager高级用法

#### 链式任务执行
```kotlin
class ChainedKeepAliveManager {
    
    fun startChainedWork(context: Context) {
        val initWork = OneTimeWorkRequestBuilder<InitializationWorker>().build()
        val keepAliveWork = OneTimeWorkRequestBuilder<KeepAliveWorker>().build()
        val cleanupWork = OneTimeWorkRequestBuilder<CleanupWorker>().build()
        
        WorkManager.getInstance(context)
            .beginWith(initWork)
            .then(keepAliveWork)
            .then(cleanupWork)
            .enqueue()
    }
}

class InitializationWorker(context: Context, params: WorkerParameters) : Worker(context, params) {
    override fun doWork(): Result {
        // 初始化工作
        return Result.success()
    }
}

class CleanupWorker(context: Context, params: WorkerParameters) : Worker(context, params) {
    override fun doWork(): Result {
        // 清理工作
        return Result.success()
    }
}
```

#### 带数据传递的Worker
```kotlin
class DataKeepAliveWorker(context: Context, params: WorkerParameters) : Worker(context, params) {
    
    override fun doWork(): Result {
        val userId = inputData.getString("user_id") ?: return Result.failure()
        val taskType = inputData.getString("task_type") ?: "default"
        
        return try {
            performTaskForUser(userId, taskType)
            
            val outputData = Data.Builder()
                .putString("result", "success")
                .putLong("timestamp", System.currentTimeMillis())
                .build()
                
            Result.success(outputData)
        } catch (e: Exception) {
            Result.failure()
        }
    }
    
    private fun performTaskForUser(userId: String, taskType: String) {
        // 执行特定用户的保活任务
    }
}

// 使用方式
fun startDataWork(context: Context, userId: String) {
    val inputData = Data.Builder()
        .putString("user_id", userId)
        .putString("task_type", "keep_alive")
        .build()
    
    val workRequest = OneTimeWorkRequestBuilder<DataKeepAliveWorker>()
        .setInputData(inputData)
        .build()
    
    WorkManager.getInstance(context).enqueue(workRequest)
}
```

---

## 3. AlarmManager - 定时任务保活

AlarmManager是Android系统提供的定时任务服务，即使在应用被杀死的情况下也能触发。

### 3.1 AlarmManager基本使用

#### 创建AlarmReceiver
```kotlin
class KeepAliveAlarmReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            ACTION_KEEP_ALIVE -> {
                handleKeepAlive(context)
            }
            ACTION_RESTART_SERVICE -> {
                restartService(context)
            }
        }
    }
    
    private fun handleKeepAlive(context: Context) {
        Log.d("KeepAliveAlarm", "执行保活逻辑")
        
        // 检查服务是否运行
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            // 重启服务
            val serviceIntent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(serviceIntent)
        }
        
        // 重新设置下一次闹钟
        scheduleNextAlarm(context)
    }
    
    private fun restartService(context: Context) {
        val serviceIntent = Intent(context, KeepAliveService::class.java)
        context.startForegroundService(serviceIntent)
    }
    
    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any { 
            it.service.className == serviceClass.name 
        }
    }
    
    private fun scheduleNextAlarm(context: Context) {
        AlarmManagerHelper.setKeepAliveAlarm(context)
    }
    
    companion object {
        const val ACTION_KEEP_ALIVE = "com.example.KEEP_ALIVE"
        const val ACTION_RESTART_SERVICE = "com.example.RESTART_SERVICE"
    }
}
```

#### AlarmManager工具类
```kotlin
class AlarmManagerHelper {
    
    companion object {
        private const val ALARM_INTERVAL = 30 * 1000L // 30秒
        private const val KEEP_ALIVE_REQUEST_CODE = 1001
        
        fun setKeepAliveAlarm(context: Context) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            
            val intent = Intent(context, KeepAliveAlarmReceiver::class.java).apply {
                action = KeepAliveAlarmReceiver.ACTION_KEEP_ALIVE
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                KEEP_ALIVE_REQUEST_CODE,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            val triggerTime = System.currentTimeMillis() + ALARM_INTERVAL
            
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    // Android 6.0+ 使用 setExactAndAllowWhileIdle
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT -> {
                    // Android 4.4+ 使用 setExact
                    alarmManager.setExact(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                }
                else -> {
                    // 旧版本使用 set
                    alarmManager.set(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                }
            }
        }
        
        fun cancelKeepAliveAlarm(context: Context) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val intent = Intent(context, KeepAliveAlarmReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                KEEP_ALIVE_REQUEST_CODE,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            alarmManager.cancel(pendingIntent)
        }
    }
}
```

### 3.2 AlarmManager高级用法

#### 智能间隔调整
```kotlin
class SmartAlarmManager {
    
    private var failureCount = 0
    private val baseInterval = 30 * 1000L // 基础间隔30秒
    private val maxInterval = 5 * 60 * 1000L // 最大间隔5分钟
    
    fun setSmartAlarm(context: Context, isSuccess: Boolean = true) {
        if (isSuccess) {
            failureCount = 0
        } else {
            failureCount++
        }
        
        val interval = calculateInterval()
        setAlarmWithInterval(context, interval)
    }
    
    private fun calculateInterval(): Long {
        // 指数退避算法
        val backoffMultiplier = Math.pow(2.0, failureCount.toDouble()).toLong()
        val calculatedInterval = baseInterval * backoffMultiplier
        return Math.min(calculatedInterval, maxInterval)
    }
    
    private fun setAlarmWithInterval(context: Context, interval: Long) {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val intent = Intent(context, KeepAliveAlarmReceiver::class.java)
        val pendingIntent = PendingIntent.getBroadcast(
            context, 1001, intent, 
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val triggerTime = System.currentTimeMillis() + interval
        alarmManager.setExactAndAllowWhileIdle(
            AlarmManager.RTC_WAKEUP,
            triggerTime,
            pendingIntent
        )
    }
}
```

---

## 4. 前台服务保活

前台服务是最可靠的保活方式之一，因为它对用户可见，系统不会轻易杀死。

### 4.1 前台服务实现

```kotlin
class KeepAliveService : Service() {
    
    private val serviceId = 1001
    private val channelId = "keep_alive_channel"
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        startForeground(serviceId, createNotification())
        
        // 启动保活逻辑
        startKeepAliveLogic()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                "应用保活服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "保持应用在后台运行"
                setShowBadge(false)
                setSound(null, null)
                enableVibration(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent, 
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, channelId)
            .setContentTitle("应用正在后台运行")
            .setContentText("点击返回应用")
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setOngoing(true) // 不可滑动删除
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }
    
    private fun startKeepAliveLogic() {
        // 启动定时任务
        AlarmManagerHelper.setKeepAliveAlarm(this)
        
        // 启动WorkManager任务
        KeepAliveManager().startKeepAliveWork(this)
    }
}
```

---

## 5. 双进程守护保活

通过创建两个进程相互监听，当一个进程被杀死时，另一个进程负责拉起。

### 5.1 主进程服务
```kotlin
class MainProcessService : Service() {
    
    private var isServiceRunning = true
    private lateinit var guardConnection: ServiceConnection
    
    override fun onCreate() {
        super.onCreate()
        startGuardService()
        startMonitorThread()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun startGuardService() {
        val intent = Intent(this, GuardProcessService::class.java)
        guardConnection = object : ServiceConnection {
            override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
                Log.d("MainProcess", "守护进程连接成功")
            }
            
            override fun onServiceDisconnected(name: ComponentName?) {
                Log.d("MainProcess", "守护进程断开连接，尝试重启")
                startGuardService()
            }
        }
        bindService(intent, guardConnection, Context.BIND_AUTO_CREATE)
    }
    
    private fun startMonitorThread() {
        Thread {
            while (isServiceRunning) {
                try {
                    // 检查守护进程是否存在
                    if (!isGuardProcessRunning()) {
                        restartGuardProcess()
                    }
                    Thread.sleep(5000) // 5秒检查一次
                } catch (e: InterruptedException) {
                    break
                }
            }
        }.start()
    }
    
    private fun isGuardProcessRunning(): Boolean {
        val manager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.runningAppProcesses?.any { 
            it.processName.contains(":guard") 
        } ?: false
    }
    
    private fun restartGuardProcess() {
        val intent = Intent(this, GuardProcessService::class.java)
        startService(intent)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        isServiceRunning = false
        try {
            unbindService(guardConnection)
        } catch (e: Exception) {
            // 忽略异常
        }
    }
}
```

### 5.2 守护进程服务
```kotlin
class GuardProcessService : Service() {
    
    private var isServiceRunning = true
    
    override fun onCreate() {
        super.onCreate()
        startMonitorThread()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = GuardBinder()
    
    private fun startMonitorThread() {
        Thread {
            while (isServiceRunning) {
                try {
                    // 检查主进程是否存在
                    if (!isMainProcessRunning()) {
                        restartMainProcess()
                    }
                    Thread.sleep(5000) // 5秒检查一次
                } catch (e: InterruptedException) {
                    break
                }
            }
        }.start()
    }
    
    private fun isMainProcessRunning(): Boolean {
        val manager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val packageName = packageName
        return manager.runningAppProcesses?.any { 
            it.processName == packageName 
        } ?: false
    }
    
    private fun restartMainProcess() {
        try {
            val intent = Intent(this, MainProcessService::class.java)
            startService(intent)
            
            // 也可以尝试启动Activity来拉起主进程
            val activityIntent = Intent(this, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            startActivity(activityIntent)
        } catch (e: Exception) {
            Log.e("GuardProcess", "重启主进程失败", e)
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        isServiceRunning = false
    }
    
    inner class GuardBinder : Binder() {
        fun getService(): GuardProcessService = this@GuardProcessService
    }
}
```

### 5.3 AndroidManifest.xml配置
```xml
<!-- 主进程服务 -->
<service
    android:name=".service.MainProcessService"
    android:enabled="true"
    android:exported="false" />

<!-- 守护进程服务 -->
<service
    android:name=".service.GuardProcessService"
    android:enabled="true"
    android:exported="false"
    android:process=":guard" />

<!-- 保活广播接收器 -->
<receiver
    android:name=".receiver.KeepAliveAlarmReceiver"
    android:enabled="true"
    android:exported="false">
    <intent-filter>
        <action android:name="com.example.KEEP_ALIVE" />
        <action android:name="com.example.RESTART_SERVICE" />
    </intent-filter>
</receiver>
```

---

## 6. 保活管理器

创建一个统一的保活管理器来协调各种保活策略。

```kotlin
class KeepAliveManager private constructor(private val context: Context) {
    
    private val sharedPreferences = context.getSharedPreferences("keep_alive", Context.MODE_PRIVATE)
    private var isKeepAliveEnabled = false
    
    companion object {
        @Volatile
        private var INSTANCE: KeepAliveManager? = null
        
        fun getInstance(context: Context): KeepAliveManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: KeepAliveManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    fun startKeepAlive() {
        if (isKeepAliveEnabled) return
        
        isKeepAliveEnabled = true
        sharedPreferences.edit().putBoolean("keep_alive_enabled", true).apply()
        
        // 1. 启动前台服务
        startForegroundService()
        
        // 2. 启动WorkManager任务
        startWorkManagerTask()
        
        // 3. 设置AlarmManager
        setAlarmManager()
        
        // 4. 启动双进程守护
        startDualProcessGuard()
        
        Log.d("KeepAliveManager", "保活机制已启动")
    }
    
    fun stopKeepAlive() {
        if (!isKeepAliveEnabled) return
        
        isKeepAliveEnabled = false
        sharedPreferences.edit().putBoolean("keep_alive_enabled", false).apply()
        
        // 停止所有保活机制
        stopForegroundService()
        stopWorkManagerTask()
        cancelAlarmManager()
        stopDualProcessGuard()
        
        Log.d("KeepAliveManager", "保活机制已停止")
    }
    
    private fun startForegroundService() {
        val intent = Intent(context, KeepAliveService::class.java)
        context.startForegroundService(intent)
    }
    
    private fun stopForegroundService() {
        val intent = Intent(context, KeepAliveService::class.java)
        context.stopService(intent)
    }
    
    private fun startWorkManagerTask() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()
        
        val workRequest = PeriodicWorkRequestBuilder<KeepAliveWorker>(15, TimeUnit.MINUTES)
            .setConstraints(constraints)
            .build()
        
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            "keep_alive_work",
            ExistingPeriodicWorkPolicy.KEEP,
            workRequest
        )
    }
    
    private fun stopWorkManagerTask() {
        WorkManager.getInstance(context).cancelUniqueWork("keep_alive_work")
    }
    
    private fun setAlarmManager() {
        AlarmManagerHelper.setKeepAliveAlarm(context)
    }
    
    private fun cancelAlarmManager() {
        AlarmManagerHelper.cancelKeepAliveAlarm(context)
    }
    
    private fun startDualProcessGuard() {
        // 启动主进程服务
        val mainIntent = Intent(context, MainProcessService::class.java)
        context.startService(mainIntent)
        
        // 启动守护进程服务
        val guardIntent = Intent(context, GuardProcessService::class.java)
        context.startService(guardIntent)
    }
    
    private fun stopDualProcessGuard() {
        context.stopService(Intent(context, MainProcessService::class.java))
        context.stopService(Intent(context, GuardProcessService::class.java))
    }
    
    fun isKeepAliveRunning(): Boolean {
        return sharedPreferences.getBoolean("keep_alive_enabled", false)
    }
}

---

## 7. 广播拉活机制

通过监听系统广播来实现应用拉活，但需要注意Android 8.0+对静态广播的严格限制。

### 7.0 Android 8.0+静态广播限制详解

#### 7.0.1 限制背景
Android 8.0 (API 26) 引入了后台执行限制，主要目的是：
- **提升设备性能**: 减少后台应用的资源消耗
- **延长电池续航**: 防止应用在后台过度活跃
- **改善用户体验**: 减少设备卡顿和发热

#### 7.0.2 被限制的静态广播
以下系统广播在Android 8.0+中**无法通过静态注册**监听：

```kotlin
// ❌ Android 8.0+中被限制的静态广播
class RestrictedBroadcasts {
    companion object {
        val RESTRICTED_BROADCASTS = listOf(
            // 网络状态变化
            "android.net.conn.CONNECTIVITY_ACTION",

            // 屏幕状态（本来就需要动态注册）
            "android.intent.action.SCREEN_ON",
            "android.intent.action.SCREEN_OFF",
            "android.intent.action.USER_PRESENT",

            // 电池状态
            "android.intent.action.ACTION_POWER_CONNECTED",
            "android.intent.action.ACTION_POWER_DISCONNECTED",
            "android.intent.action.BATTERY_LOW",
            "android.intent.action.BATTERY_OKAY",

            // 应用安装卸载（部分限制）
            "android.intent.action.PACKAGE_ADDED",
            "android.intent.action.PACKAGE_REMOVED",
            "android.intent.action.PACKAGE_CHANGED",

            // 时间变化
            "android.intent.action.TIME_SET",
            "android.intent.action.TIMEZONE_CHANGED",

            // 存储状态
            "android.intent.action.MEDIA_MOUNTED",
            "android.intent.action.MEDIA_UNMOUNTED"
        )
    }
}
```

#### 7.0.3 仍然可用的静态广播
以下广播在Android 8.0+中**仍可静态注册**：

```kotlin
// ✅ Android 8.0+中仍可用的静态广播
class AllowedBroadcasts {
    companion object {
        val ALLOWED_BROADCASTS = listOf(
            // 开机启动（最重要的保活广播）
            "android.intent.action.BOOT_COMPLETED",
            "android.intent.action.QUICKBOOT_POWERON",
            "com.htc.intent.action.QUICKBOOT_POWERON",

            // 应用自身相关
            "android.intent.action.MY_PACKAGE_REPLACED",
            "android.intent.action.MY_PACKAGE_SUSPENDED",
            "android.intent.action.MY_PACKAGE_UNSUSPENDED",

            // 语言区域变化
            "android.intent.action.LOCALE_CHANGED",

            // 用户解锁
            "android.intent.action.USER_UNLOCKED",

            // 短信接收（需要权限）
            "android.provider.Telephony.SMS_RECEIVED",

            // 来电（需要权限）
            "android.intent.action.PHONE_STATE"
        )
    }
}
```

#### 7.0.4 应对策略

**策略1: 使用仍可用的静态广播**
```xml
<!-- AndroidManifest.xml中仍可用的静态广播 -->
<receiver
    android:name=".receiver.BootCompletedReceiver"
    android:enabled="true"
    android:exported="false">
    <intent-filter android:priority="1000">
        <!-- 开机启动 - 最重要的保活入口 -->
        <action android:name="android.intent.action.BOOT_COMPLETED" />
        <action android:name="android.intent.action.QUICKBOOT_POWERON" />
        <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />

        <!-- 应用替换更新 -->
        <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
        <data android:scheme="package" />

        <!-- 用户解锁 -->
        <action android:name="android.intent.action.USER_UNLOCKED" />
    </intent-filter>
</receiver>
```

**策略2: 动态注册关键广播**
```kotlin
class DynamicBroadcastManager(private val context: Context) {

    private var connectivityReceiver: BroadcastReceiver? = null
    private var screenReceiver: BroadcastReceiver? = null
    private var batteryReceiver: BroadcastReceiver? = null

    fun registerDynamicBroadcasts() {
        // 注册网络状态广播
        registerConnectivityBroadcast()

        // 注册屏幕状态广播
        registerScreenBroadcast()

        // 注册电池状态广播
        registerBatteryBroadcast()
    }

    private fun registerConnectivityBroadcast() {
        connectivityReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                when (intent.action) {
                    ConnectivityManager.CONNECTIVITY_ACTION -> {
                        handleNetworkChange(context, intent)
                    }
                }
            }
        }

        val filter = IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
        context.registerReceiver(connectivityReceiver, filter)
        Log.d("DynamicBroadcast", "网络状态广播已动态注册")
    }

    private fun registerScreenBroadcast() {
        screenReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                when (intent.action) {
                    Intent.ACTION_SCREEN_ON -> handleScreenOn(context)
                    Intent.ACTION_SCREEN_OFF -> handleScreenOff(context)
                    Intent.ACTION_USER_PRESENT -> handleUserPresent(context)
                }
            }
        }

        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_ON)
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_USER_PRESENT)
        }
        context.registerReceiver(screenReceiver, filter)
        Log.d("DynamicBroadcast", "屏幕状态广播已动态注册")
    }

    private fun registerBatteryBroadcast() {
        batteryReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                when (intent.action) {
                    Intent.ACTION_POWER_CONNECTED -> handlePowerConnected(context)
                    Intent.ACTION_POWER_DISCONNECTED -> handlePowerDisconnected(context)
                    Intent.ACTION_BATTERY_LOW -> handleBatteryLow(context)
                    Intent.ACTION_BATTERY_OKAY -> handleBatteryOkay(context)
                }
            }
        }

        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_POWER_CONNECTED)
            addAction(Intent.ACTION_POWER_DISCONNECTED)
            addAction(Intent.ACTION_BATTERY_LOW)
            addAction(Intent.ACTION_BATTERY_OKAY)
        }
        context.registerReceiver(batteryReceiver, filter)
        Log.d("DynamicBroadcast", "电池状态广播已动态注册")
    }

    fun unregisterDynamicBroadcasts() {
        try {
            connectivityReceiver?.let { context.unregisterReceiver(it) }
            screenReceiver?.let { context.unregisterReceiver(it) }
            batteryReceiver?.let { context.unregisterReceiver(it) }

            connectivityReceiver = null
            screenReceiver = null
            batteryReceiver = null

            Log.d("DynamicBroadcast", "动态广播已全部注销")
        } catch (e: Exception) {
            Log.e("DynamicBroadcast", "注销动态广播失败", e)
        }
    }

    private fun handleNetworkChange(context: Context, intent: Intent) {
        val isConnected = isNetworkAvailable(context)
        Log.d("DynamicBroadcast", "网络状态变化: ${if (isConnected) "已连接" else "已断开"}")

        if (isConnected) {
            // 网络恢复时重启相关服务
            restartNetworkDependentServices(context)
        }
    }

    private fun handleScreenOn(context: Context) {
        Log.d("DynamicBroadcast", "屏幕点亮")
        // 屏幕点亮时的拉活逻辑
        performScreenOnRevive(context)
    }

    private fun handleScreenOff(context: Context) {
        Log.d("DynamicBroadcast", "屏幕关闭")
        // 屏幕关闭时确保服务运行
        ensureServicesRunning(context)
    }

    private fun handleUserPresent(context: Context) {
        Log.d("DynamicBroadcast", "用户解锁")
        // 用户解锁时的拉活逻辑
        performUserPresentRevive(context)
    }

    private fun handlePowerConnected(context: Context) {
        Log.d("DynamicBroadcast", "电源已连接")
        // 充电时可以执行更多保活操作
        performChargingRevive(context)
    }

    private fun handlePowerDisconnected(context: Context) {
        Log.d("DynamicBroadcast", "电源已断开")
        // 断电时切换到节能模式
        switchToPowerSavingMode(context)
    }

    private fun handleBatteryLow(context: Context) {
        Log.d("DynamicBroadcast", "电池电量低")
        // 低电量时减少保活活动
        reduceBatteryConsumption(context)
    }

    private fun handleBatteryOkay(context: Context) {
        Log.d("DynamicBroadcast", "电池电量正常")
        // 电量恢复时恢复正常保活
        restoreNormalKeepAlive(context)
    }

    // 辅助方法实现...
    private fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.isConnected == true
        }
    }

    private fun restartNetworkDependentServices(context: Context) {
        // 重启依赖网络的服务
        HeartbeatManager.getInstance().startHeartbeat()
        // 重启其他网络相关服务...
    }

    private fun performScreenOnRevive(context: Context) {
        // 屏幕点亮时的拉活逻辑
        KeepAliveManager.getInstance(context).startKeepAlive()
    }

    private fun ensureServicesRunning(context: Context) {
        // 确保关键服务正在运行
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            val intent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(intent)
        }
    }

    private fun performUserPresentRevive(context: Context) {
        // 用户解锁时的拉活逻辑
        KeepAliveManager.getInstance(context).startKeepAlive()

        // 可以启动透明Activity
        val intent = Intent(context, TransparentActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_NO_HISTORY
        }
        context.startActivity(intent)
    }

    private fun performChargingRevive(context: Context) {
        // 充电时的保活逻辑
        KeepAliveManager.getInstance(context).startKeepAlive()

        // 充电时可以执行更多任务
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            JobSchedulerHelper.scheduleChargingJob(context)
        }
    }

    private fun switchToPowerSavingMode(context: Context) {
        // 切换到节能模式
        Log.d("DynamicBroadcast", "切换到节能模式")

        // 降低心跳频率
        HeartbeatManager.getInstance().stopHeartbeat()

        // 调整其他保活策略...
    }

    private fun reduceBatteryConsumption(context: Context) {
        // 减少电池消耗
        Log.d("DynamicBroadcast", "减少电池消耗")

        // 停止非必要的后台任务
        HeartbeatManager.getInstance().stopHeartbeat()

        // 清理内存
        MemoryOptimizationManager.optimizeMemoryUsage(context)
    }

    private fun restoreNormalKeepAlive(context: Context) {
        // 恢复正常保活
        Log.d("DynamicBroadcast", "恢复正常保活模式")

        KeepAliveManager.getInstance(context).startKeepAlive()
        HeartbeatManager.getInstance().startHeartbeat()
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }
}
```

**策略3: 使用替代方案**
```kotlin
---

## 6. Doze 和 App Standby 模式详解

### 6.1 Doze 模式概述

Doze 模式是Android 6.0 (API 23)引入的电池优化功能，当设备满足以下条件时会进入Doze模式：
- **屏幕关闭**
- **设备静止不动**（通过加速度计检测）
- **设备未充电**
- **一段时间无用户交互**

#### 6.1.1 Doze 模式的影响

```kotlin
class DozeImpactAnalyzer {

    companion object {
        fun analyzeDozeImpact() {
            Log.d("DozeImpact", """
                Doze模式对应用的影响：

                ❌ 被限制的功能：
                - 网络访问被阻止
                - AlarmManager延迟执行
                - JobScheduler延迟执行
                - 同步适配器停止工作
                - WiFi扫描停止

                ✅ 不受影响的功能：
                - 前台服务继续运行
                - 高优先级FCM消息
                - setExactAndAllowWhileIdle()的闹钟
                - setAlarmClock()的闹钟

                📱 Doze模式级别：
                1. Light Doze (Android 7.0+)
                2. Deep Doze (Android 6.0+)
            """.trimIndent())
        }
    }
}
```

#### 6.1.2 Doze 模式检测

```kotlin
class DozeDetector(private val context: Context) {

    fun isDeviceInDozeMode(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            powerManager.isDeviceIdleMode
        } else {
            false
        }
    }

    fun isAppInStandbyMode(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val usageStatsManager = context.getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
            usageStatsManager.isAppInactive(context.packageName)
        } else {
            false
        }
    }

    fun isIgnoringBatteryOptimizations(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            powerManager.isIgnoringBatteryOptimizations(context.packageName)
        } else {
            true
        }
    }

    fun getDozeWhitelistStatus(): String {
        return when {
            isIgnoringBatteryOptimizations() -> "已加入白名单"
            isDeviceInDozeMode() -> "设备处于Doze模式"
            isAppInStandbyMode() -> "应用处于Standby模式"
            else -> "正常状态"
        }
    }
}
```

### 6.2 App Standby 模式

App Standby是针对单个应用的省电机制，当应用满足以下条件时会进入Standby模式：
- **用户长时间未使用应用**
- **应用没有前台服务**
- **应用没有活跃的通知**
- **应用不在白名单中**

#### 6.2.1 App Standby 的影响

```kotlin
class AppStandbyManager {

    companion object {
        fun getStandbyImpact(): String {
            return """
                App Standby模式的影响：

                ❌ 被限制的功能：
                - 网络访问受限
                - 后台同步停止
                - JobScheduler延迟执行
                - AlarmManager延迟执行

                ✅ 不受影响的功能：
                - 前台服务
                - 高优先级推送消息
                - 用户主动启动应用
                - 系统级应用

                🔄 恢复条件：
                - 用户主动启动应用
                - 应用启动前台服务
                - 应用显示通知
                - 设备充电时短暂恢复
            """.trimIndent()
        }
    }
}
```

### 6.3 对WorkManager的影响

#### 6.3.1 WorkManager在Doze模式下的行为

```kotlin
class DozeAwareWorkManager {

    fun scheduleDozeCompatibleWork(context: Context) {
        // WorkManager会自动处理Doze模式
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .setRequiresBatteryNotLow(true) // 电量不低时执行
            .setRequiresCharging(false)
            .build()

        val dozeAwareWork = PeriodicWorkRequestBuilder<DozeAwareWorker>(
            15, TimeUnit.MINUTES, // 最小间隔15分钟
            5, TimeUnit.MINUTES   // 弹性间隔5分钟
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL,
                WorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .build()

        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            "doze_aware_work",
            ExistingPeriodicWorkPolicy.KEEP,
            dozeAwareWork
        )
    }
}

class DozeAwareWorker(context: Context, params: WorkerParameters) : Worker(context, params) {

    override fun doWork(): Result {
        val dozeDetector = DozeDetector(applicationContext)

        return try {
            Log.d("DozeAwareWorker", "开始执行任务")
            Log.d("DozeAwareWorker", "Doze状态: ${dozeDetector.getDozeWhitelistStatus()}")

            // 检查设备状态
            if (dozeDetector.isDeviceInDozeMode()) {
                Log.w("DozeAwareWorker", "设备处于Doze模式，任务可能被延迟")
                // 在Doze模式下执行轻量级任务
                performLightweightTask()
            } else {
                // 正常模式下执行完整任务
                performFullTask()
            }

            Result.success()
        } catch (e: Exception) {
            Log.e("DozeAwareWorker", "任务执行失败", e)
            Result.retry()
        }
    }

    private fun performLightweightTask() {
        // 在Doze模式下只执行必要的轻量级任务
        Log.d("DozeAwareWorker", "执行轻量级任务")

        // 检查关键服务状态
        checkCriticalServices()

        // 更新本地状态
        updateLocalStatus()
    }

    private fun performFullTask() {
        // 正常模式下执行完整任务
        Log.d("DozeAwareWorker", "执行完整任务")

        // 检查并重启服务
        checkAndRestartServices()

        // 发送心跳包
        sendHeartbeat()

        // 同步数据
        syncData()

        // 清理缓存
        cleanupCache()
    }

    private fun checkCriticalServices() {
        // 检查关键服务
        val context = applicationContext
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            Log.w("DozeAwareWorker", "关键服务未运行")
        }
    }

    private fun updateLocalStatus() {
        // 更新本地状态
        val sharedPrefs = applicationContext.getSharedPreferences("worker_status", Context.MODE_PRIVATE)
        sharedPrefs.edit()
            .putLong("last_lightweight_execution", System.currentTimeMillis())
            .apply()
    }

    private fun checkAndRestartServices() {
        val context = applicationContext
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            val intent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(intent)
        }
    }

    private fun sendHeartbeat() {
        try {
            HeartbeatManager.getInstance().sendHeartbeat()
        } catch (e: Exception) {
            Log.e("DozeAwareWorker", "发送心跳失败", e)
        }
    }

    private fun syncData() {
        // 数据同步逻辑
        Log.d("DozeAwareWorker", "执行数据同步")
    }

    private fun cleanupCache() {
        // 缓存清理逻辑
        MemoryOptimizationManager.optimizeMemoryUsage(applicationContext)
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }
}
```

### 6.4 对JobScheduler的影响

#### 6.4.1 JobScheduler在Doze模式下的行为

```kotlin
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
class DozeAwareJobScheduler {

    companion object {
        private const val DOZE_AWARE_JOB_ID = 3001
        private const val MAINTENANCE_WINDOW_JOB_ID = 3002

        fun scheduleDozeAwareJob(context: Context) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) return

            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler

            // 在Doze模式下，JobScheduler会被延迟到维护窗口期执行
            val jobInfo = JobInfo.Builder(DOZE_AWARE_JOB_ID, ComponentName(context, DozeAwareJobService::class.java))
                .setMinimumLatency(30 * 1000) // 最小延迟30秒
                .setOverrideDeadline(2 * 60 * 1000) // 最大延迟2分钟
                .setRequiredNetworkType(JobInfo.NETWORK_TYPE_NONE)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false) // 不要求设备空闲
                .setPersisted(true)
                .build()

            val result = jobScheduler.schedule(jobInfo)
            if (result == JobScheduler.RESULT_SUCCESS) {
                Log.d("DozeAwareJobScheduler", "Doze感知任务调度成功")
            } else {
                Log.e("DozeAwareJobScheduler", "Doze感知任务调度失败")
            }
        }

        fun scheduleMaintenanceWindowJob(context: Context) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) return

            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler

            // 利用维护窗口期执行重要任务
            val jobInfo = JobInfo.Builder(MAINTENANCE_WINDOW_JOB_ID, ComponentName(context, DozeAwareJobService::class.java))
                .setPeriodic(15 * 60 * 1000) // 15分钟周期
                .setRequiredNetworkType(JobInfo.NETWORK_TYPE_ANY) // 需要网络
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(true) // 要求设备空闲（维护窗口期）
                .setPersisted(true)
                .build()

            jobScheduler.schedule(jobInfo)
            Log.d("DozeAwareJobScheduler", "维护窗口任务调度成功")
        }
    }
}

@TargetApi(Build.VERSION_CODES.LOLLIPOP)
class DozeAwareJobService : JobService() {

    override fun onStartJob(params: JobParameters?): Boolean {
        Log.d("DozeAwareJobService", "任务开始执行，ID: ${params?.jobId}")

        Thread {
            try {
                when (params?.jobId) {
                    3001 -> handleDozeAwareTask(params)
                    3002 -> handleMaintenanceTask(params)
                }
            } catch (e: Exception) {
                Log.e("DozeAwareJobService", "任务执行失败", e)
                jobFinished(params, true) // 重新调度
            }
        }.start()

        return true
    }

    override fun onStopJob(params: JobParameters?): Boolean {
        Log.d("DozeAwareJobService", "任务被停止，ID: ${params?.jobId}")
        return true // 需要重新调度
    }

    private fun handleDozeAwareTask(params: JobParameters) {
        val dozeDetector = DozeDetector(this)

        Log.d("DozeAwareJobService", "执行Doze感知任务")
        Log.d("DozeAwareJobService", "当前状态: ${dozeDetector.getDozeWhitelistStatus()}")

        if (dozeDetector.isDeviceInDozeMode()) {
            // 在Doze模式下执行最小化任务
            performMinimalTask()
        } else {
            // 正常模式下执行标准任务
            performStandardTask()
        }

        jobFinished(params, false)

        // 重新调度下一次任务
        DozeAwareJobScheduler.scheduleDozeAwareJob(this)
    }

    private fun handleMaintenanceTask(params: JobParameters) {
        Log.d("DozeAwareJobService", "执行维护窗口任务")

        // 在维护窗口期执行重要的保活任务
        performMaintenanceTask()

        jobFinished(params, false)
    }

    private fun performMinimalTask() {
        Log.d("DozeAwareJobService", "执行最小化任务")

        // 只检查关键服务状态
        checkCriticalServiceStatus()

        // 更新本地时间戳
        updateLocalTimestamp()
    }

    private fun performStandardTask() {
        Log.d("DozeAwareJobService", "执行标准任务")

        // 检查并重启服务
        checkAndRestartServices()

        // 发送心跳包
        sendHeartbeat()

        // 执行保活逻辑
        performKeepAliveLogic()
    }

    private fun performMaintenanceTask() {
        Log.d("DozeAwareJobService", "执行维护任务")

        // 在维护窗口期执行完整的保活检查
        performFullKeepAliveCheck()

        // 清理和优化
        performCleanupAndOptimization()

        // 数据同步
        performDataSync()
    }

    private fun checkCriticalServiceStatus() {
        if (!isServiceRunning(KeepAliveService::class.java)) {
            Log.w("DozeAwareJobService", "关键服务未运行")
        }
    }

    private fun updateLocalTimestamp() {
        val sharedPrefs = getSharedPreferences("job_status", Context.MODE_PRIVATE)
        sharedPrefs.edit()
            .putLong("last_minimal_execution", System.currentTimeMillis())
            .apply()
    }

    private fun checkAndRestartServices() {
        if (!isServiceRunning(KeepAliveService::class.java)) {
            val intent = Intent(this, KeepAliveService::class.java)
            startForegroundService(intent)
        }
    }

    private fun sendHeartbeat() {
        try {
            HeartbeatManager.getInstance().sendHeartbeat()
        } catch (e: Exception) {
            Log.e("DozeAwareJobService", "发送心跳失败", e)
        }
    }

    private fun performKeepAliveLogic() {
        KeepAliveManager.getInstance(this).startKeepAlive()
    }

    private fun performFullKeepAliveCheck() {
        // 完整的保活检查
        KeepAliveManager.getInstance(this).startKeepAlive()

        // 检查所有服务状态
        checkAllServicesStatus()
    }

    private fun performCleanupAndOptimization() {
        // 内存优化
        MemoryOptimizationManager.optimizeMemoryUsage(this)

        // 缓存清理
        clearUnnecessaryCache()
    }

    private fun performDataSync() {
        // 数据同步逻辑
        Log.d("DozeAwareJobService", "执行数据同步")
    }

    private fun checkAllServicesStatus() {
        val services = listOf(
            KeepAliveService::class.java,
            MainProcessService::class.java,
            GuardProcessService::class.java
        )

        services.forEach { serviceClass ->
            if (!isServiceRunning(serviceClass)) {
                Log.w("DozeAwareJobService", "${serviceClass.simpleName} 未运行")
            }
        }
    }

    private fun clearUnnecessaryCache() {
        // 清理不必要的缓存
        Log.d("DozeAwareJobService", "清理缓存")
    }

    private fun isServiceRunning(serviceClass: Class<*>): Boolean {
        val manager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }
}
```

class BroadcastAlternativeManager {

    companion object {
        fun setupAlternatives(context: Context) {
            // 1. 使用WorkManager替代网络状态监听
            setupNetworkConstraintWork(context)

            // 2. 使用JobScheduler替代定时广播
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                JobSchedulerHelper.scheduleNetworkJob(context)
            }

            // 3. 使用前台服务保持活跃
            startPersistentForegroundService(context)
        }

        private fun setupNetworkConstraintWork(context: Context) {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .build()

            val networkWork = PeriodicWorkRequestBuilder<NetworkMonitorWorker>(15, TimeUnit.MINUTES)
                .setConstraints(constraints)
                .build()

            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                "network_monitor_work",
                ExistingPeriodicWorkPolicy.KEEP,
                networkWork
            )
        }

        private fun startPersistentForegroundService(context: Context) {
            val intent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(intent)
        }
    }
}

class NetworkMonitorWorker(context: Context, params: WorkerParameters) : Worker(context, params) {

    override fun doWork(): Result {
        return try {
            // 网络可用时执行的保活逻辑
            Log.d("NetworkMonitorWorker", "网络可用，执行保活任务")

            // 检查并重启服务
            checkAndRestartServices()

            // 发送心跳
            sendHeartbeat()

            Result.success()
        } catch (e: Exception) {
            Log.e("NetworkMonitorWorker", "网络监控任务失败", e)
            Result.retry()
        }
    }

    private fun checkAndRestartServices() {
        // 检查并重启关键服务
        val context = applicationContext
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            val intent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(intent)
        }
    }

    private fun sendHeartbeat() {
        // 发送心跳包
        HeartbeatManager.getInstance().sendHeartbeat()
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }
}

### 6.5 对AlarmManager的影响

#### 6.5.1 AlarmManager在Doze模式下的行为

```kotlin
class DozeAwareAlarmManager {

    companion object {
        private const val NORMAL_ALARM_REQUEST_CODE = 4001
        private const val DOZE_IMMUNE_ALARM_REQUEST_CODE = 4002
        private const val ALARM_CLOCK_REQUEST_CODE = 4003

        fun scheduleDozeAwareAlarm(context: Context) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

            // 普通闹钟在Doze模式下会被延迟
            scheduleNormalAlarm(context, alarmManager)

            // 使用setExactAndAllowWhileIdle避免Doze模式影响
            scheduleDozeImmuneAlarm(context, alarmManager)

            // 使用setAlarmClock确保在Doze模式下也能执行
            scheduleAlarmClockAlarm(context, alarmManager)
        }

        private fun scheduleNormalAlarm(context: Context, alarmManager: AlarmManager) {
            val intent = Intent(context, DozeAwareAlarmReceiver::class.java).apply {
                action = "NORMAL_ALARM"
            }

            val pendingIntent = PendingIntent.getBroadcast(
                context,
                NORMAL_ALARM_REQUEST_CODE,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val triggerTime = System.currentTimeMillis() + 60 * 1000 // 1分钟后

            // 普通闹钟在Doze模式下会被延迟到维护窗口期
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT -> {
                    alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
                }
                else -> {
                    alarmManager.set(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
                }
            }

            Log.d("DozeAwareAlarm", "普通闹钟已设置（可能被Doze延迟）")
        }

        private fun scheduleDozeImmuneAlarm(context: Context, alarmManager: AlarmManager) {
            val intent = Intent(context, DozeAwareAlarmReceiver::class.java).apply {
                action = "DOZE_IMMUNE_ALARM"
            }

            val pendingIntent = PendingIntent.getBroadcast(
                context,
                DOZE_IMMUNE_ALARM_REQUEST_CODE,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val triggerTime = System.currentTimeMillis() + 30 * 1000 // 30秒后

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // setExactAndAllowWhileIdle可以在Doze模式下执行
                // 但有频率限制：每个应用每15分钟最多1次
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
                Log.d("DozeAwareAlarm", "Doze免疫闹钟已设置")
            } else {
                // Android 6.0以下使用普通setExact
                alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
                Log.d("DozeAwareAlarm", "普通精确闹钟已设置")
            }
        }

        private fun scheduleAlarmClockAlarm(context: Context, alarmManager: AlarmManager) {
            val intent = Intent(context, DozeAwareAlarmReceiver::class.java).apply {
                action = "ALARM_CLOCK_ALARM"
            }

            val pendingIntent = PendingIntent.getBroadcast(
                context,
                ALARM_CLOCK_REQUEST_CODE,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val triggerTime = System.currentTimeMillis() + 2 * 60 * 1000 // 2分钟后

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                // setAlarmClock会在Doze模式下执行，但会显示在状态栏
                val alarmClockInfo = AlarmManager.AlarmClockInfo(
                    triggerTime,
                    pendingIntent // 点击时的意图
                )

                alarmManager.setAlarmClock(alarmClockInfo, pendingIntent)
                Log.d("DozeAwareAlarm", "AlarmClock闹钟已设置（会显示在状态栏）")
            } else {
                // 旧版本使用普通闹钟
                alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
                Log.d("DozeAwareAlarm", "普通闹钟已设置（旧版本）")
            }
        }

        fun cancelAllAlarms(context: Context) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

            // 取消所有闹钟
            val requestCodes = listOf(
                NORMAL_ALARM_REQUEST_CODE,
                DOZE_IMMUNE_ALARM_REQUEST_CODE,
                ALARM_CLOCK_REQUEST_CODE
            )

            requestCodes.forEach { requestCode ->
                val intent = Intent(context, DozeAwareAlarmReceiver::class.java)
                val pendingIntent = PendingIntent.getBroadcast(
                    context,
                    requestCode,
                    intent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
                alarmManager.cancel(pendingIntent)
            }

            Log.d("DozeAwareAlarm", "所有闹钟已取消")
        }
    }
}

class DozeAwareAlarmReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        val dozeDetector = DozeDetector(context)

        Log.d("DozeAwareAlarmReceiver", "收到闹钟广播: $action")
        Log.d("DozeAwareAlarmReceiver", "当前状态: ${dozeDetector.getDozeWhitelistStatus()}")

        when (action) {
            "NORMAL_ALARM" -> {
                handleNormalAlarm(context)
            }
            "DOZE_IMMUNE_ALARM" -> {
                handleDozeImmuneAlarm(context)
            }
            "ALARM_CLOCK_ALARM" -> {
                handleAlarmClockAlarm(context)
            }
        }
    }

    private fun handleNormalAlarm(context: Context) {
        Log.d("DozeAwareAlarmReceiver", "处理普通闹钟")

        // 执行轻量级保活检查
        performLightweightKeepAlive(context)

        // 重新设置下一次闹钟
        Handler(Looper.getMainLooper()).postDelayed({
            DozeAwareAlarmManager.scheduleDozeAwareAlarm(context)
        }, 1000)
    }

    private fun handleDozeImmuneAlarm(context: Context) {
        Log.d("DozeAwareAlarmReceiver", "处理Doze免疫闹钟")

        // 执行重要的保活任务
        performImportantKeepAlive(context)

        // 注意：setExactAndAllowWhileIdle有频率限制
        // 每个应用每15分钟最多1次，所以要谨慎使用
        Handler(Looper.getMainLooper()).postDelayed({
            DozeAwareAlarmManager.scheduleDozeAwareAlarm(context)
        }, 15 * 60 * 1000) // 15分钟后重新设置
    }

    private fun handleAlarmClockAlarm(context: Context) {
        Log.d("DozeAwareAlarmReceiver", "处理AlarmClock闹钟")

        // 执行完整的保活检查
        performFullKeepAlive(context)

        // AlarmClock闹钟不应该频繁使用，因为会显示在状态栏
        // 只在关键时刻使用
    }

    private fun performLightweightKeepAlive(context: Context) {
        // 轻量级保活检查
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            Log.w("DozeAwareAlarmReceiver", "关键服务未运行，尝试重启")
            val intent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(intent)
        }

        // 更新最后活跃时间
        updateLastActiveTime(context)
    }

    private fun performImportantKeepAlive(context: Context) {
        // 重要的保活任务
        performLightweightKeepAlive(context)

        // 检查网络状态
        if (isNetworkAvailable(context)) {
            // 发送心跳包
            try {
                HeartbeatManager.getInstance().sendHeartbeat()
            } catch (e: Exception) {
                Log.e("DozeAwareAlarmReceiver", "发送心跳失败", e)
            }
        }

        // 启动WorkManager任务
        KeepAliveManager().startKeepAliveWork(context)
    }

    private fun performFullKeepAlive(context: Context) {
        // 完整的保活检查
        performImportantKeepAlive(context)

        // 启动保活管理器
        KeepAliveManager.getInstance(context).startKeepAlive()

        // 检查所有服务状态
        checkAllServices(context)
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }

    private fun updateLastActiveTime(context: Context) {
        val sharedPrefs = context.getSharedPreferences("alarm_status", Context.MODE_PRIVATE)
        sharedPrefs.edit()
            .putLong("last_alarm_time", System.currentTimeMillis())
            .apply()
    }

    private fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.isConnected == true
        }
    }

    private fun checkAllServices(context: Context) {
        val services = listOf(
            KeepAliveService::class.java,
            MainProcessService::class.java,
            GuardProcessService::class.java
        )

        services.forEach { serviceClass ->
            if (!isServiceRunning(context, serviceClass)) {
                Log.w("DozeAwareAlarmReceiver", "${serviceClass.simpleName} 未运行")

                // 尝试重启服务
                val intent = Intent(context, serviceClass)
                if (serviceClass == KeepAliveService::class.java) {
                    context.startForegroundService(intent)
                } else {
                    context.startService(intent)
                }
            }
        }
    }
}
```

#### 6.5.2 AlarmManager频率限制

```kotlin
class AlarmFrequencyManager {

    companion object {
        fun getAlarmLimitations(): String {
            return """
                AlarmManager在Doze模式下的限制：

                📱 普通闹钟 (setExact):
                - 在Doze模式下被延迟到维护窗口期
                - 维护窗口期大约每1-2小时一次
                - 设备充电时会有短暂的维护窗口

                ⚡ setExactAndAllowWhileIdle:
                - 可以在Doze模式下执行
                - 频率限制：每个应用每15分钟最多1次
                - 适用于重要的保活任务

                🔔 setAlarmClock:
                - 在Doze模式下正常执行
                - 会在状态栏显示闹钟图标
                - 不应频繁使用，影响用户体验

                💡 最佳实践：
                - 优先使用WorkManager和JobScheduler
                - 谨慎使用setExactAndAllowWhileIdle
                - 避免频繁使用setAlarmClock
                - 结合前台服务提高可靠性
            """.trimIndent()
        }

        fun createSmartAlarmStrategy(context: Context) {
            val dozeDetector = DozeDetector(context)

            when {
                dozeDetector.isIgnoringBatteryOptimizations() -> {
                    // 已加入白名单，可以使用普通闹钟
                    scheduleNormalFrequencyAlarm(context)
                }
                dozeDetector.isDeviceInDozeMode() -> {
                    // 设备在Doze模式，使用免疫闹钟
                    scheduleDozeImmuneAlarmWithLimit(context)
                }
                else -> {
                    // 正常状态，使用标准策略
                    scheduleStandardAlarm(context)
                }
            }
        }

        private fun scheduleNormalFrequencyAlarm(context: Context) {
            // 白名单应用可以使用较高频率
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val intent = Intent(context, DozeAwareAlarmReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                context, 5001, intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val triggerTime = System.currentTimeMillis() + 30 * 1000 // 30秒间隔
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)

            Log.d("AlarmFrequency", "设置正常频率闹钟（30秒间隔）")
        }

        private fun scheduleDozeImmuneAlarmWithLimit(context: Context) {
            // 遵守15分钟限制
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val intent = Intent(context, DozeAwareAlarmReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                context, 5002, intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val triggerTime = System.currentTimeMillis() + 15 * 60 * 1000 // 15分钟间隔

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    triggerTime,
                    pendingIntent
                )
            }

            Log.d("AlarmFrequency", "设置Doze免疫闹钟（15分钟间隔）")
        }

        private fun scheduleStandardAlarm(context: Context) {
            // 标准策略：平衡频率和可靠性
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val intent = Intent(context, DozeAwareAlarmReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                context, 5003, intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val triggerTime = System.currentTimeMillis() + 2 * 60 * 1000 // 2分钟间隔

            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                }
                else -> {
                    alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
                }
            }

            Log.d("AlarmFrequency", "设置标准闹钟（2分钟间隔）")
        }
    }
}
```

### 6.6 Doze和App Standby的应对策略

#### 6.6.1 综合应对方案

```kotlin
class DozeAndStandbyManager(private val context: Context) {

    private val dozeDetector = DozeDetector(context)

    fun setupComprehensiveStrategy() {
        Log.d("DozeAndStandby", "设置综合应对策略")

        // 1. 检查当前状态
        val currentStatus = getCurrentStatus()
        Log.d("DozeAndStandby", "当前状态: $currentStatus")

        // 2. 根据状态选择策略
        when {
            dozeDetector.isIgnoringBatteryOptimizations() -> {
                setupWhitelistStrategy()
            }
            dozeDetector.isDeviceInDozeMode() -> {
                setupDozeStrategy()
            }
            dozeDetector.isAppInStandbyMode() -> {
                setupStandbyStrategy()
            }
            else -> {
                setupNormalStrategy()
            }
        }

        // 3. 启动监控
        startStatusMonitoring()
    }

    private fun getCurrentStatus(): String {
        return buildString {
            appendLine("设备状态检查:")
            appendLine("- Doze模式: ${dozeDetector.isDeviceInDozeMode()}")
            appendLine("- App Standby: ${dozeDetector.isAppInStandbyMode()}")
            appendLine("- 电池优化白名单: ${dozeDetector.isIgnoringBatteryOptimizations()}")
            appendLine("- 网络状态: ${isNetworkAvailable()}")
            appendLine("- 充电状态: ${isCharging()}")
        }
    }

    private fun setupWhitelistStrategy() {
        Log.d("DozeAndStandby", "设置白名单策略")

        // 白名单应用可以使用更积极的保活策略
        // 1. 使用正常频率的AlarmManager
        AlarmFrequencyManager.scheduleNormalFrequencyAlarm(context)

        // 2. 启动所有保活机制
        KeepAliveManager.getInstance(context).startKeepAlive()

        // 3. 使用WorkManager
        DozeAwareWorkManager().scheduleDozeCompatibleWork(context)

        // 4. 使用JobScheduler
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            DozeAwareJobScheduler.scheduleDozeAwareJob(context)
        }
    }

    private fun setupDozeStrategy() {
        Log.d("DozeAndStandby", "设置Doze模式策略")

        // Doze模式下的保守策略
        // 1. 使用Doze免疫闹钟（频率限制）
        DozeAwareAlarmManager.scheduleDozeAwareAlarm(context)

        // 2. 依赖前台服务
        startForegroundService()

        // 3. 使用WorkManager（会自动处理Doze）
        DozeAwareWorkManager().scheduleDozeCompatibleWork(context)

        // 4. 减少后台活动
        reduceBackgroundActivity()
    }

    private fun setupStandbyStrategy() {
        Log.d("DozeAndStandby", "设置Standby模式策略")

        // App Standby模式下的策略
        // 1. 启动前台服务（可以退出Standby）
        startForegroundService()

        // 2. 显示通知（可以退出Standby）
        showKeepAliveNotification()

        // 3. 使用最小化的保活机制
        setupMinimalKeepAlive()
    }

    private fun setupNormalStrategy() {
        Log.d("DozeAndStandby", "设置正常策略")

        // 正常状态下的标准策略
        KeepAliveManager.getInstance(context).startKeepAlive()
    }

    private fun startStatusMonitoring() {
        // 定期检查Doze和Standby状态
        Thread {
            while (true) {
                try {
                    val status = getCurrentStatus()
                    Log.d("DozeAndStandby", "状态监控: $status")

                    // 根据状态变化调整策略
                    adjustStrategyIfNeeded()

                    Thread.sleep(5 * 60 * 1000) // 5分钟检查一次
                } catch (e: InterruptedException) {
                    break
                } catch (e: Exception) {
                    Log.e("DozeAndStandby", "状态监控异常", e)
                }
            }
        }.start()
    }

    private fun adjustStrategyIfNeeded() {
        // 根据状态变化动态调整策略
        val currentDozeState = dozeDetector.isDeviceInDozeMode()
        val currentStandbyState = dozeDetector.isAppInStandbyMode()

        // 保存状态变化历史，用于策略调整
        saveStatusHistory(currentDozeState, currentStandbyState)
    }

    private fun startForegroundService() {
        val intent = Intent(context, KeepAliveService::class.java).apply {
            putExtra("doze_aware", true)
        }
        context.startForegroundService(intent)
    }

    private fun showKeepAliveNotification() {
        // 显示保活通知，帮助退出App Standby
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                "keep_alive_channel",
                "应用保活",
                NotificationManager.IMPORTANCE_LOW
            )
            notificationManager.createNotificationChannel(channel)
        }

        val notification = NotificationCompat.Builder(context, "keep_alive_channel")
            .setContentTitle("应用保活")
            .setContentText("应用正在后台运行")
            .setSmallIcon(R.drawable.ic_notification)
            .setOngoing(true)
            .build()

        notificationManager.notify(9001, notification)
    }

    private fun setupMinimalKeepAlive() {
        // 最小化的保活机制
        // 只启动最必要的服务
        startForegroundService()

        // 使用最低频率的AlarmManager
        DozeAwareAlarmManager.scheduleDozeAwareAlarm(context)
    }

    private fun reduceBackgroundActivity() {
        // 减少后台活动
        // 停止非必要的后台任务
        HeartbeatManager.getInstance().stopHeartbeat()

        // 清理内存
        MemoryOptimizationManager.optimizeMemoryUsage(context)
    }

    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.isConnected == true
        }
    }

    private fun isCharging(): Boolean {
        val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        return batteryManager.isCharging
    }

    private fun saveStatusHistory(dozeState: Boolean, standbyState: Boolean) {
        val sharedPrefs = context.getSharedPreferences("doze_standby_history", Context.MODE_PRIVATE)
        sharedPrefs.edit()
            .putBoolean("last_doze_state", dozeState)
            .putBoolean("last_standby_state", standbyState)
            .putLong("last_check_time", System.currentTimeMillis())
            .apply()
    }
}
```

### 6.7 网络访问受限详解

在Doze和App Standby模式下，"网络访问受限"是应用保活面临的重要挑战。以下详细说明网络限制的具体表现和应对方法：

#### 6.7.1 网络受限的具体表现

```kotlin
class NetworkLimitationExamples {

    companion object {
        fun demonstrateNetworkLimitations() {
            Log.d("NetworkLimitation", """
                网络访问受限的具体表现：

                🚫 Deep Doze模式：
                - HTTP/HTTPS请求被完全阻止
                - Socket连接被强制断开
                - WebSocket连接中断
                - 下载/上传任务停止
                - 实时通信中断

                ⏳ Light Doze/App Standby：
                - API调用延迟1-6小时
                - 数据同步推迟到维护窗口
                - 图片/文件下载暂停
                - 推送消息接收延迟

                📊 App Standby桶分类影响：
                - RARE桶：每天只有几次网络访问机会
                - RESTRICTED桶：几乎无网络访问

                ❌ 常见网络异常：
                - ConnectException: Connection refused
                - SocketTimeoutException: timeout
                - UnknownHostException: Unable to resolve host
                - IOException: Network is unreachable
            """.trimIndent())
        }
    }
}
```

#### 6.7.2 网络请求失败处理

```kotlin
class DozeAwareNetworkManager {

    fun makeNetworkRequest(url: String): NetworkResult {
        return try {
            val connection = URL(url).openConnection() as HttpURLConnection
            connection.connectTimeout = 5000
            connection.readTimeout = 10000

            val responseCode = connection.responseCode
            val response = connection.inputStream.bufferedReader().use { it.readText() }

            NetworkResult.Success(response)

        } catch (e: IOException) {
            Log.e("NetworkManager", "网络请求失败: ${e.message}")

            when (e) {
                is ConnectException -> {
                    Log.w("NetworkManager", "连接被拒绝 - 可能是Doze模式")
                    handleDozeNetworkBlock(url)
                    NetworkResult.DozeBlocked
                }
                is SocketTimeoutException -> {
                    Log.w("NetworkManager", "连接超时 - 网络访问受限")
                    handleNetworkTimeout(url)
                    NetworkResult.Timeout
                }
                is UnknownHostException -> {
                    Log.w("NetworkManager", "无法解析主机 - DNS查询被阻止")
                    handleDnsBlock(url)
                    NetworkResult.DnsBlocked
                }
                else -> {
                    Log.e("NetworkManager", "其他网络错误: ${e.javaClass.simpleName}")
                    NetworkResult.Error(e.message ?: "未知错误")
                }
            }
        }
    }

    private fun handleDozeNetworkBlock(url: String) {
        // 1. 将请求加入队列，等待维护窗口期
        NetworkTaskQueue.getInstance().addTask(NetworkTask {
            makeNetworkRequest(url)
        })

        // 2. 尝试启动前台服务
        startForegroundServiceForNetwork()

        // 3. 记录Doze阻止事件
        recordDozeBlockEvent(url)
    }

    private fun handleNetworkTimeout(url: String) {
        // 使用指数退避重试
        scheduleRetryWithBackoff(url)
    }

    private fun handleDnsBlock(url: String) {
        // 切换到离线模式或使用缓存
        switchToOfflineMode(url)
    }

    private fun startForegroundServiceForNetwork() {
        // 启动前台服务以豁免网络限制
        val intent = Intent(context, NetworkForegroundService::class.java)
        context.startForegroundService(intent)
    }

    private fun recordDozeBlockEvent(url: String) {
        // 记录Doze阻止事件，用于分析
        val sharedPrefs = context.getSharedPreferences("network_blocks", Context.MODE_PRIVATE)
        val blockCount = sharedPrefs.getInt("doze_block_count", 0)
        sharedPrefs.edit()
            .putInt("doze_block_count", blockCount + 1)
            .putLong("last_doze_block", System.currentTimeMillis())
            .putString("last_blocked_url", url)
            .apply()
    }

    sealed class NetworkResult {
        data class Success(val data: String) : NetworkResult()
        object DozeBlocked : NetworkResult()
        object Timeout : NetworkResult()
        object DnsBlocked : NetworkResult()
        data class Error(val message: String) : NetworkResult()
    }
}
```

#### 6.7.3 网络状态智能检测

```kotlin
class SmartNetworkDetector(private val context: Context) {

    fun getComprehensiveNetworkStatus(): NetworkStatusReport {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val dozeDetector = DozeDetector(context)

        return NetworkStatusReport(
            isPhysicallyConnected = isPhysicallyConnected(connectivityManager),
            isLogicallyAccessible = isLogicallyAccessible(),
            dozeMode = dozeDetector.isDeviceInDozeMode(),
            standbyMode = dozeDetector.isAppInStandbyMode(),
            whitelisted = dozeDetector.isIgnoringBatteryOptimizations(),
            networkType = getNetworkType(connectivityManager),
            signalStrength = getSignalStrength(),
            estimatedAccessLevel = calculateAccessLevel()
        )
    }

    private fun isPhysicallyConnected(connectivityManager: ConnectivityManager): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            network != null
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.isConnected == true
        }
    }

    private fun isLogicallyAccessible(): Boolean {
        // 尝试实际的网络访问来检测逻辑可达性
        return try {
            val url = URL("https://www.google.com")
            val connection = url.openConnection()
            connection.connectTimeout = 3000
            connection.readTimeout = 3000

            val responseCode = (connection as HttpURLConnection).responseCode
            responseCode == 200
        } catch (e: Exception) {
            false
        }
    }

    private fun getNetworkType(connectivityManager: ConnectivityManager): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)

            when {
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> "WiFi"
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> "移动网络"
                capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) == true -> "以太网"
                else -> "未知"
            }
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.typeName ?: "未知"
        }
    }

    private fun getSignalStrength(): String {
        // 获取信号强度（简化实现）
        return "良好" // 实际实现需要使用TelephonyManager等
    }

    private fun calculateAccessLevel(): NetworkAccessLevel {
        val dozeDetector = DozeDetector(context)

        return when {
            dozeDetector.isIgnoringBatteryOptimizations() -> NetworkAccessLevel.UNRESTRICTED
            dozeDetector.isDeviceInDozeMode() -> NetworkAccessLevel.BLOCKED
            dozeDetector.isAppInStandbyMode() -> NetworkAccessLevel.LIMITED
            isLogicallyAccessible() -> NetworkAccessLevel.NORMAL
            else -> NetworkAccessLevel.UNAVAILABLE
        }
    }

    data class NetworkStatusReport(
        val isPhysicallyConnected: Boolean,
        val isLogicallyAccessible: Boolean,
        val dozeMode: Boolean,
        val standbyMode: Boolean,
        val whitelisted: Boolean,
        val networkType: String,
        val signalStrength: String,
        val estimatedAccessLevel: NetworkAccessLevel
    ) {
        fun getDetailedDescription(): String {
            return buildString {
                appendLine("📊 网络状态详细报告:")
                appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
                appendLine("🔌 物理连接: ${if (isPhysicallyConnected) "✅ 已连接" else "❌ 未连接"}")
                appendLine("🌐 逻辑可达: ${if (isLogicallyAccessible) "✅ 可访问" else "❌ 不可访问"}")
                appendLine("📡 网络类型: $networkType")
                appendLine("📶 信号强度: $signalStrength")
                appendLine("🔋 Doze模式: ${if (dozeMode) "🚫 是" else "✅ 否"}")
                appendLine("⏸️ Standby模式: ${if (standbyMode) "⏳ 是" else "✅ 否"}")
                appendLine("⚡ 电池白名单: ${if (whitelisted) "✅ 已加入" else "❌ 未加入"}")
                appendLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
                append("🎯 综合评估: ${estimatedAccessLevel.getDescription()}")
            }
        }
    }

    enum class NetworkAccessLevel(private val description: String) {
        UNRESTRICTED("✅ 无限制访问"),
        NORMAL("✅ 正常访问"),
        LIMITED("⏳ 受限访问"),
        BLOCKED("🚫 访问被阻止"),
        UNAVAILABLE("❌ 网络不可用");

        fun getDescription(): String = description
    }
}
```

#### 7.0.5 开机启动广播的特殊处理

开机启动是最重要的保活入口，需要特别注意：

```kotlin
class BootCompletedReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        Log.d("BootCompleted", "收到开机广播: ${intent.action}")

        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED -> {
                handleBootCompleted(context)
            }
            Intent.ACTION_QUICKBOOT_POWERON -> {
                handleQuickBoot(context)
            }
            "com.htc.intent.action.QUICKBOOT_POWERON" -> {
                handleHtcQuickBoot(context)
            }
            Intent.ACTION_MY_PACKAGE_REPLACED -> {
                handlePackageReplaced(context)
            }
            Intent.ACTION_USER_UNLOCKED -> {
                handleUserUnlocked(context)
            }
        }
    }

    private fun handleBootCompleted(context: Context) {
        Log.d("BootCompleted", "系统开机完成")

        // 延迟启动，避免系统启动时的资源竞争
        Handler(Looper.getMainLooper()).postDelayed({
            startKeepAliveSystem(context)
        }, 10000) // 延迟10秒
    }

    private fun handleQuickBoot(context: Context) {
        Log.d("BootCompleted", "快速开机完成")
        startKeepAliveSystem(context)
    }

    private fun handleHtcQuickBoot(context: Context) {
        Log.d("BootCompleted", "HTC快速开机完成")
        startKeepAliveSystem(context)
    }

    private fun handlePackageReplaced(context: Context) {
        Log.d("BootCompleted", "应用包已替换")
        startKeepAliveSystem(context)
    }

    private fun handleUserUnlocked(context: Context) {
        Log.d("BootCompleted", "用户已解锁")
        startKeepAliveSystem(context)
    }

    private fun startKeepAliveSystem(context: Context) {
        try {
            // 1. 启动保活管理器
            KeepAliveManager.getInstance(context).startKeepAlive()

            // 2. 注册动态广播
            val dynamicManager = DynamicBroadcastManager(context)
            dynamicManager.registerDynamicBroadcasts()

            // 3. 启动前台服务
            val serviceIntent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(serviceIntent)

            // 4. 设置AlarmManager
            AlarmManagerHelper.setKeepAliveAlarm(context)

            // 5. 启动WorkManager任务
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                KeepAliveManager().startKeepAliveWork(context)
            }

            // 6. 启动JobScheduler任务
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                JobSchedulerHelper.scheduleKeepAliveJob(context)
            }

            Log.d("BootCompleted", "保活系统启动完成")

        } catch (e: Exception) {
            Log.e("BootCompleted", "启动保活系统失败", e)
        }
    }
}
```

#### 7.0.6 版本兼容性处理

```kotlin
class BroadcastCompatibilityManager {

    companion object {
        fun setupBroadcastStrategy(context: Context) {
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                    // Android 8.0+: 主要依靠动态广播和其他机制
                    setupAndroid8PlusStrategy(context)
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    // Android 6.0-7.x: 可以使用更多静态广播
                    setupAndroid6To7Strategy(context)
                }
                else -> {
                    // Android 6.0以下: 可以使用所有静态广播
                    setupLegacyStrategy(context)
                }
            }
        }

        private fun setupAndroid8PlusStrategy(context: Context) {
            Log.d("BroadcastCompat", "设置Android 8.0+广播策略")

            // 1. 只注册允许的静态广播（开机启动等）
            // 2. 动态注册其他重要广播
            val dynamicManager = DynamicBroadcastManager(context)
            dynamicManager.registerDynamicBroadcasts()

            // 3. 使用WorkManager替代部分广播功能
            BroadcastAlternativeManager.setupAlternatives(context)

            // 4. 加强前台服务保活
            startEnhancedForegroundService(context)
        }

        private fun setupAndroid6To7Strategy(context: Context) {
            Log.d("BroadcastCompat", "设置Android 6.0-7.x广播策略")

            // 可以使用更多静态广播，但仍需要注意电池优化
            val dynamicManager = DynamicBroadcastManager(context)
            dynamicManager.registerDynamicBroadcasts()

            // 同时使用JobScheduler
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                JobSchedulerHelper.scheduleKeepAliveJob(context)
            }
        }

        private fun setupLegacyStrategy(context: Context) {
            Log.d("BroadcastCompat", "设置传统广播策略")

            // 旧版本可以使用所有静态广播
            val dynamicManager = DynamicBroadcastManager(context)
            dynamicManager.registerDynamicBroadcasts()

            // 同时使用传统的保活方法
            startLegacyKeepAliveServices(context)
        }

        private fun startEnhancedForegroundService(context: Context) {
            val intent = Intent(context, KeepAliveService::class.java).apply {
                putExtra("enhanced_mode", true)
            }
            context.startForegroundService(intent)
        }

        private fun startLegacyKeepAliveServices(context: Context) {
            // 启动传统的保活服务
            val intent = Intent(context, KeepAliveService::class.java)
            context.startService(intent)

            // 启动双进程守护
            val mainIntent = Intent(context, MainProcessService::class.java)
            context.startService(mainIntent)

            val guardIntent = Intent(context, GuardProcessService::class.java)
            context.startService(guardIntent)
        }
    }
}
```

#### 7.0.7 动态广播生命周期管理

```kotlin
class BroadcastLifecycleManager private constructor() {

    private val registeredReceivers = mutableMapOf<String, BroadcastReceiver>()
    private var isRegistered = false

    companion object {
        @Volatile
        private var INSTANCE: BroadcastLifecycleManager? = null

        fun getInstance(): BroadcastLifecycleManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: BroadcastLifecycleManager().also { INSTANCE = it }
            }
        }
    }

    fun registerAllBroadcasts(context: Context) {
        if (isRegistered) {
            Log.w("BroadcastLifecycle", "广播已注册，跳过重复注册")
            return
        }

        try {
            // 注册网络状态广播
            registerNetworkBroadcast(context)

            // 注册屏幕状态广播
            registerScreenBroadcast(context)

            // 注册电池状态广播
            registerBatteryBroadcast(context)

            // 注册时间变化广播
            registerTimeBroadcast(context)

            isRegistered = true
            Log.d("BroadcastLifecycle", "所有动态广播注册完成")

        } catch (e: Exception) {
            Log.e("BroadcastLifecycle", "注册动态广播失败", e)
        }
    }

    fun unregisterAllBroadcasts(context: Context) {
        if (!isRegistered) {
            Log.w("BroadcastLifecycle", "广播未注册，跳过注销")
            return
        }

        try {
            registeredReceivers.values.forEach { receiver ->
                context.unregisterReceiver(receiver)
            }
            registeredReceivers.clear()
            isRegistered = false

            Log.d("BroadcastLifecycle", "所有动态广播注销完成")

        } catch (e: Exception) {
            Log.e("BroadcastLifecycle", "注销动态广播失败", e)
        }
    }

    private fun registerNetworkBroadcast(context: Context) {
        val receiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                handleNetworkChange(context)
            }
        }

        val filter = IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
        context.registerReceiver(receiver, filter)
        registeredReceivers["network"] = receiver
    }

    private fun registerScreenBroadcast(context: Context) {
        val receiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                when (intent.action) {
                    Intent.ACTION_SCREEN_ON -> handleScreenOn(context)
                    Intent.ACTION_SCREEN_OFF -> handleScreenOff(context)
                    Intent.ACTION_USER_PRESENT -> handleUserPresent(context)
                }
            }
        }

        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_ON)
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_USER_PRESENT)
        }
        context.registerReceiver(receiver, filter)
        registeredReceivers["screen"] = receiver
    }

    private fun registerBatteryBroadcast(context: Context) {
        val receiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                when (intent.action) {
                    Intent.ACTION_POWER_CONNECTED -> handlePowerConnected(context)
                    Intent.ACTION_POWER_DISCONNECTED -> handlePowerDisconnected(context)
                    Intent.ACTION_BATTERY_LOW -> handleBatteryLow(context)
                    Intent.ACTION_BATTERY_OKAY -> handleBatteryOkay(context)
                }
            }
        }

        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_POWER_CONNECTED)
            addAction(Intent.ACTION_POWER_DISCONNECTED)
            addAction(Intent.ACTION_BATTERY_LOW)
            addAction(Intent.ACTION_BATTERY_OKAY)
        }
        context.registerReceiver(receiver, filter)
        registeredReceivers["battery"] = receiver
    }

    private fun registerTimeBroadcast(context: Context) {
        val receiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                when (intent.action) {
                    Intent.ACTION_TIME_TICK -> handleTimeTick(context)
                    Intent.ACTION_TIME_CHANGED -> handleTimeChanged(context)
                    Intent.ACTION_TIMEZONE_CHANGED -> handleTimezoneChanged(context)
                }
            }
        }

        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_TIME_TICK)
            addAction(Intent.ACTION_TIME_CHANGED)
            addAction(Intent.ACTION_TIMEZONE_CHANGED)
        }
        context.registerReceiver(receiver, filter)
        registeredReceivers["time"] = receiver
    }

    // 处理方法
    private fun handleNetworkChange(context: Context) {
        Log.d("BroadcastLifecycle", "网络状态变化")
        KeepAliveManager.getInstance(context).startKeepAlive()
    }

    private fun handleScreenOn(context: Context) {
        Log.d("BroadcastLifecycle", "屏幕点亮")
        KeepAliveManager.getInstance(context).startKeepAlive()
    }

    private fun handleScreenOff(context: Context) {
        Log.d("BroadcastLifecycle", "屏幕关闭")
        // 屏幕关闭时确保服务运行
    }

    private fun handleUserPresent(context: Context) {
        Log.d("BroadcastLifecycle", "用户解锁")
        KeepAliveManager.getInstance(context).startKeepAlive()
    }

    private fun handlePowerConnected(context: Context) {
        Log.d("BroadcastLifecycle", "电源连接")
        KeepAliveManager.getInstance(context).startKeepAlive()
    }

    private fun handlePowerDisconnected(context: Context) {
        Log.d("BroadcastLifecycle", "电源断开")
        // 切换到节能模式
    }

    private fun handleBatteryLow(context: Context) {
        Log.d("BroadcastLifecycle", "电池电量低")
        // 减少保活活动
    }

    private fun handleBatteryOkay(context: Context) {
        Log.d("BroadcastLifecycle", "电池电量正常")
        KeepAliveManager.getInstance(context).startKeepAlive()
    }

    private fun handleTimeTick(context: Context) {
        // 每分钟触发一次，可用于轻量级保活检查
        if (System.currentTimeMillis() % (5 * 60 * 1000) == 0L) {
            // 每5分钟执行一次保活检查
            performLightweightKeepAliveCheck(context)
        }
    }

    private fun handleTimeChanged(context: Context) {
        Log.d("BroadcastLifecycle", "系统时间变化")
        KeepAliveManager.getInstance(context).startKeepAlive()
    }

    private fun handleTimezoneChanged(context: Context) {
        Log.d("BroadcastLifecycle", "时区变化")
        KeepAliveManager.getInstance(context).startKeepAlive()
    }

    private fun performLightweightKeepAliveCheck(context: Context) {
        // 轻量级保活检查
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            val intent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(intent)
        }
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }
}
```

### 7.1 系统广播监听（更新版）

```kotlin
class SystemBroadcastReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        Log.d("SystemBroadcast", "收到系统广播: ${intent.action}")

        when (intent.action) {
            Intent.ACTION_SCREEN_ON -> {
                handleScreenOn(context)
            }
            Intent.ACTION_SCREEN_OFF -> {
                handleScreenOff(context)
            }
            Intent.ACTION_USER_PRESENT -> {
                handleUserPresent(context)
            }
            ConnectivityManager.CONNECTIVITY_ACTION -> {
                handleNetworkChange(context)
            }
            Intent.ACTION_POWER_CONNECTED -> {
                handlePowerConnected(context)
            }
            Intent.ACTION_POWER_DISCONNECTED -> {
                handlePowerDisconnected(context)
            }
        }
    }

    private fun handleScreenOn(context: Context) {
        // 屏幕点亮时的拉活逻辑
        restartServicesIfNeeded(context)
    }

    private fun handleScreenOff(context: Context) {
        // 屏幕关闭时的保活逻辑
        ensureServicesRunning(context)
    }

    private fun handleUserPresent(context: Context) {
        // 用户解锁时的拉活逻辑
        restartServicesIfNeeded(context)

        // 可以在这里启动一个透明Activity来拉活
        startTransparentActivity(context)
    }

    private fun handleNetworkChange(context: Context) {
        // 网络状态变化时的拉活逻辑
        if (isNetworkAvailable(context)) {
            restartServicesIfNeeded(context)
        }
    }

    private fun handlePowerConnected(context: Context) {
        // 充电时的拉活逻辑
        restartServicesIfNeeded(context)
    }

    private fun handlePowerDisconnected(context: Context) {
        // 断开充电时的保活逻辑
        ensureServicesRunning(context)
    }

    private fun restartServicesIfNeeded(context: Context) {
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            val intent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(intent)
        }

        // 重新设置AlarmManager
        AlarmManagerHelper.setKeepAliveAlarm(context)
    }

    private fun ensureServicesRunning(context: Context) {
        // 确保关键服务正在运行
        restartServicesIfNeeded(context)
    }

    private fun startTransparentActivity(context: Context) {
        val intent = Intent(context, TransparentActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_NO_HISTORY
        }
        context.startActivity(intent)
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }

    private fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.isConnected == true
        }
    }
}
```

### 7.2 动态注册广播

```kotlin
class BroadcastManager(private val context: Context) {

    private var systemBroadcastReceiver: SystemBroadcastReceiver? = null
    private var isRegistered = false

    fun registerBroadcasts() {
        if (isRegistered) return

        systemBroadcastReceiver = SystemBroadcastReceiver()
        val intentFilter = IntentFilter().apply {
            // 屏幕相关
            addAction(Intent.ACTION_SCREEN_ON)
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_USER_PRESENT)

            // 网络相关
            addAction(ConnectivityManager.CONNECTIVITY_ACTION)

            // 电源相关
            addAction(Intent.ACTION_POWER_CONNECTED)
            addAction(Intent.ACTION_POWER_DISCONNECTED)

            // 其他系统事件
            addAction(Intent.ACTION_BOOT_COMPLETED)
            addAction(Intent.ACTION_MY_PACKAGE_REPLACED)
            addAction(Intent.ACTION_PACKAGE_REPLACED)
            addDataScheme("package")
        }

        context.registerReceiver(systemBroadcastReceiver, intentFilter)
        isRegistered = true

        Log.d("BroadcastManager", "系统广播已注册")
    }

    fun unregisterBroadcasts() {
        if (!isRegistered || systemBroadcastReceiver == null) return

        try {
            context.unregisterReceiver(systemBroadcastReceiver)
            isRegistered = false
            systemBroadcastReceiver = null
            Log.d("BroadcastManager", "系统广播已注销")
        } catch (e: Exception) {
            Log.e("BroadcastManager", "注销广播失败", e)
        }
    }
}
```

### 7.3 透明Activity拉活

```kotlin
class TransparentActivity : Activity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置透明主题
        window.setFlags(
            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
        )

        // 执行拉活逻辑
        performReviveLogic()

        // 立即关闭Activity
        finish()
    }

    private fun performReviveLogic() {
        // 启动保活服务
        val intent = Intent(this, KeepAliveService::class.java)
        startForegroundService(intent)

        // 启动保活管理器
        KeepAliveManager.getInstance(this).startKeepAlive()

        Log.d("TransparentActivity", "透明Activity拉活完成")
    }

    override fun onResume() {
        super.onResume()
        // 确保Activity不可见
        moveTaskToBack(true)
    }
}
```

---

## 8. 账户同步拉活

通过Android的账户同步机制来实现应用拉活，这是一种相对隐蔽的方法。

### 8.1 创建同步适配器

```kotlin
class KeepAliveSyncAdapter(
    context: Context,
    autoInitialize: Boolean
) : AbstractThreadedSyncAdapter(context, autoInitialize) {

    override fun onPerformSync(
        account: Account?,
        extras: Bundle?,
        authority: String?,
        provider: ContentProviderClient?,
        syncResult: SyncResult?
    ) {
        Log.d("SyncAdapter", "执行同步任务")

        try {
            // 执行保活逻辑
            performKeepAliveSync()

            // 启动服务
            restartServices()

            // 设置下次同步
            scheduleNextSync(account, authority)

        } catch (e: Exception) {
            Log.e("SyncAdapter", "同步任务执行失败", e)
            syncResult?.stats?.numIoExceptions++
        }
    }

    private fun performKeepAliveSync() {
        // 模拟数据同步
        Thread.sleep(1000)

        // 检查应用状态
        checkAppStatus()

        // 发送心跳
        sendHeartbeat()
    }

    private fun restartServices() {
        val context = context
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            val intent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(intent)
        }
    }

    private fun scheduleNextSync(account: Account?, authority: String?) {
        if (account != null && authority != null) {
            // 设置定期同步
            ContentResolver.addPeriodicSync(
                account,
                authority,
                Bundle.EMPTY,
                60 * 15 // 15分钟同步一次
            )
        }
    }

    private fun checkAppStatus() {
        // 检查应用各模块状态
    }

    private fun sendHeartbeat() {
        // 发送心跳包
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }
}
```

### 8.2 同步服务

```kotlin
class KeepAliveSyncService : Service() {

    private var syncAdapter: KeepAliveSyncAdapter? = null

    override fun onCreate() {
        super.onCreate()
        synchronized(this) {
            if (syncAdapter == null) {
                syncAdapter = KeepAliveSyncAdapter(applicationContext, true)
            }
        }
    }

    override fun onBind(intent: Intent?): IBinder? {
        return syncAdapter?.syncAdapterBinder
    }
}
```

### 8.3 认证器

```kotlin
class KeepAliveAuthenticator(context: Context) : AbstractAccountAuthenticator(context) {

    override fun editProperties(response: AccountAuthenticatorResponse?, accountType: String?): Bundle? {
        return null
    }

    override fun addAccount(
        response: AccountAuthenticatorResponse?,
        accountType: String?,
        authTokenType: String?,
        requiredFeatures: Array<out String>?,
        options: Bundle?
    ): Bundle? {
        return null
    }

    override fun confirmCredentials(
        response: AccountAuthenticatorResponse?,
        account: Account?,
        options: Bundle?
    ): Bundle? {
        return null
    }

    override fun getAuthToken(
        response: AccountAuthenticatorResponse?,
        account: Account?,
        authTokenType: String?,
        options: Bundle?
    ): Bundle? {
        return null
    }

    override fun getAuthTokenLabel(authTokenType: String?): String? {
        return null
    }

    override fun updateCredentials(
        response: AccountAuthenticatorResponse?,
        account: Account?,
        authTokenType: String?,
        options: Bundle?
    ): Bundle? {
        return null
    }

    override fun hasFeatures(
        response: AccountAuthenticatorResponse?,
        account: Account?,
        features: Array<out String>?
    ): Bundle? {
        return null
    }
}

class KeepAliveAuthenticatorService : Service() {

    private var authenticator: KeepAliveAuthenticator? = null

    override fun onCreate() {
        super.onCreate()
        authenticator = KeepAliveAuthenticator(this)
    }

    override fun onBind(intent: Intent?): IBinder? {
        return authenticator?.iBinder
    }
}
```

### 8.4 内容提供者

```kotlin
class KeepAliveContentProvider : ContentProvider() {

    companion object {
        const val AUTHORITY = "com.example.keepalive.provider"
        private val uriMatcher = UriMatcher(UriMatcher.NO_MATCH)

        init {
            uriMatcher.addURI(AUTHORITY, "data", 1)
        }
    }

    override fun onCreate(): Boolean {
        return true
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        // 在查询时执行保活逻辑
        performKeepAliveCheck()
        return null
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        return 0
    }

    private fun performKeepAliveCheck() {
        val context = context ?: return

        // 检查服务状态
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            val intent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(intent)
        }
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }
}
```

---

## 9. 厂商适配和白名单

不同厂商对后台应用有不同的限制策略，需要针对性适配。

### 9.1 厂商检测工具

```kotlin
class ManufacturerHelper {

    companion object {
        fun getManufacturer(): String {
            return Build.MANUFACTURER.lowercase()
        }

        fun isHuawei(): Boolean {
            return getManufacturer().contains("huawei")
        }

        fun isXiaomi(): Boolean {
            return getManufacturer().contains("xiaomi")
        }

        fun isOppo(): Boolean {
            return getManufacturer().contains("oppo")
        }

        fun isVivo(): Boolean {
            return getManufacturer().contains("vivo")
        }

        fun isSamsung(): Boolean {
            return getManufacturer().contains("samsung")
        }

        fun isOnePlus(): Boolean {
            return getManufacturer().contains("oneplus")
        }

        fun isMeizu(): Boolean {
            return getManufacturer().contains("meizu")
        }

        fun needsWhitelistGuidance(): Boolean {
            return isHuawei() || isXiaomi() || isOppo() || isVivo() || isOnePlus() || isMeizu()
        }
    }
}
```

### 9.2 白名单引导

```kotlin
class WhitelistGuideManager(private val context: Context) {

    fun showWhitelistGuide() {
        if (!ManufacturerHelper.needsWhitelistGuidance()) {
            return
        }

        val manufacturer = ManufacturerHelper.getManufacturer()
        val guideMessage = getGuideMessage(manufacturer)

        AlertDialog.Builder(context)
            .setTitle("应用保活设置")
            .setMessage(guideMessage)
            .setPositiveButton("去设置") { _, _ ->
                openWhitelistSettings()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun getGuideMessage(manufacturer: String): String {
        return when {
            ManufacturerHelper.isHuawei() -> {
                "为了确保应用正常运行，请将本应用加入以下白名单：\n" +
                "1. 设置 → 电池 → 启动管理 → 手动管理\n" +
                "2. 设置 → 应用 → 权限管理 → 自启动管理\n" +
                "3. 手机管家 → 电池管理 → 受保护应用"
            }
            ManufacturerHelper.isXiaomi() -> {
                "为了确保应用正常运行，请进行以下设置：\n" +
                "1. 设置 → 应用设置 → 授权管理 → 自启动管理\n" +
                "2. 设置 → 电量和性能 → 应用配置 → 无限制\n" +
                "3. 安全中心 → 电量 → 应用智能省电 → 无限制"
            }
            ManufacturerHelper.isOppo() -> {
                "为了确保应用正常运行，请进行以下设置：\n" +
                "1. 设置 → 电池 → 耗电保护 → 允许后台运行\n" +
                "2. 设置 → 应用管理 → 自启动管理\n" +
                "3. 手机管家 → 权限隐私 → 自启动管理"
            }
            ManufacturerHelper.isVivo() -> {
                "为了确保应用正常运行，请进行以下设置：\n" +
                "1. 设置 → 电池 → 后台耗电管理 → 允许后台高耗电\n" +
                "2. 设置 → 更多设置 → 权限管理 → 自启动\n" +
                "3. i管家 → 应用管理 → 自启动管理"
            }
            else -> {
                "为了确保应用正常运行，请将本应用加入系统白名单，允许自启动和后台运行。"
            }
        }
    }

    private fun openWhitelistSettings() {
        try {
            when {
                ManufacturerHelper.isHuawei() -> openHuaweiSettings()
                ManufacturerHelper.isXiaomi() -> openXiaomiSettings()
                ManufacturerHelper.isOppo() -> openOppoSettings()
                ManufacturerHelper.isVivo() -> openVivoSettings()
                else -> openGeneralSettings()
            }
        } catch (e: Exception) {
            Log.e("WhitelistGuide", "打开设置页面失败", e)
            openGeneralSettings()
        }
    }

    private fun openHuaweiSettings() {
        val intent = Intent().apply {
            component = ComponentName(
                "com.huawei.systemmanager",
                "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity"
            )
        }
        context.startActivity(intent)
    }

    private fun openXiaomiSettings() {
        val intent = Intent().apply {
            component = ComponentName(
                "com.miui.securitycenter",
                "com.miui.permcenter.autostart.AutoStartManagementActivity"
            )
        }
        context.startActivity(intent)
    }

    private fun openOppoSettings() {
        val intent = Intent().apply {
            component = ComponentName(
                "com.coloros.safecenter",
                "com.coloros.safecenter.permission.startup.StartupAppListActivity"
            )
        }
        context.startActivity(intent)
    }

    private fun openVivoSettings() {
        val intent = Intent().apply {
            component = ComponentName(
                "com.vivo.permissionmanager",
                "com.vivo.permissionmanager.activity.BgStartUpManagerActivity"
            )
        }
        context.startActivity(intent)
    }

    private fun openGeneralSettings() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", context.packageName, null)
        }
        context.startActivity(intent)
    }
}

---

## 10. 进程优先级管理

通过提高进程优先级来降低被系统杀死的概率。

### 10.1 进程优先级提升

```kotlin
class ProcessPriorityManager {

    companion object {
        fun setProcessPriority(priority: Int) {
            try {
                Process.setThreadPriority(priority)
                Log.d("ProcessPriority", "进程优先级设置为: $priority")
            } catch (e: Exception) {
                Log.e("ProcessPriority", "设置进程优先级失败", e)
            }
        }

        fun setHighPriority() {
            setProcessPriority(Process.THREAD_PRIORITY_URGENT_AUDIO)
        }

        fun setNormalPriority() {
            setProcessPriority(Process.THREAD_PRIORITY_DEFAULT)
        }

        fun setLowPriority() {
            setProcessPriority(Process.THREAD_PRIORITY_BACKGROUND)
        }

        fun getCurrentPriority(): Int {
            return Process.getThreadPriority(Process.myTid())
        }

        fun getProcessInfo(context: Context): String {
            val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val processInfo = manager.runningAppProcesses?.find {
                it.pid == Process.myPid()
            }

            return processInfo?.let {
                "进程名: ${it.processName}\n" +
                "PID: ${it.pid}\n" +
                "重要性: ${getImportanceDescription(it.importance)}\n" +
                "LRU: ${it.lru}"
            } ?: "无法获取进程信息"
        }

        private fun getImportanceDescription(importance: Int): String {
            return when (importance) {
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND -> "前台进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND_SERVICE -> "前台服务进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_TOP_SLEEPING -> "顶层休眠进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_VISIBLE -> "可见进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_PERCEPTIBLE -> "可感知进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_CANT_SAVE_STATE -> "无法保存状态进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_SERVICE -> "服务进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_CACHED -> "缓存进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_GONE -> "已销毁进程"
                else -> "未知($importance)"
            }
        }
    }
}
```

### 10.2 内存管理优化

```kotlin
class MemoryOptimizationManager {

    companion object {
        fun optimizeMemoryUsage(context: Context) {
            // 1. 清理不必要的缓存
            clearUnnecessaryCache()

            // 2. 释放大对象
            releaseLargeObjects()

            // 3. 触发垃圾回收
            System.gc()

            // 4. 监控内存使用
            monitorMemoryUsage(context)
        }

        private fun clearUnnecessaryCache() {
            // 清理图片缓存
            // 清理网络缓存
            // 清理临时文件
            Log.d("MemoryOptimization", "清理缓存完成")
        }

        private fun releaseLargeObjects() {
            // 释放大型对象引用
            // 清理静态变量
            Log.d("MemoryOptimization", "释放大对象完成")
        }

        fun monitorMemoryUsage(context: Context) {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val availableMemory = maxMemory - usedMemory

            Log.d("MemoryMonitor", """
                系统可用内存: ${memoryInfo.availMem / 1024 / 1024}MB
                系统总内存: ${memoryInfo.totalMem / 1024 / 1024}MB
                系统内存不足: ${memoryInfo.lowMemory}
                应用已用内存: ${usedMemory / 1024 / 1024}MB
                应用最大内存: ${maxMemory / 1024 / 1024}MB
                应用可用内存: ${availableMemory / 1024 / 1024}MB
            """.trimIndent())

            // 如果内存使用过高，执行清理
            if (usedMemory > maxMemory * 0.8) {
                Log.w("MemoryMonitor", "内存使用过高，执行清理")
                clearUnnecessaryCache()
                System.gc()
            }
        }

        fun registerMemoryPressureCallback(context: Context) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
                (context as? Application)?.registerComponentCallbacks(object : ComponentCallbacks2 {
                    override fun onConfigurationChanged(newConfig: Configuration) {}

                    override fun onLowMemory() {
                        Log.w("MemoryPressure", "系统内存不足")
                        optimizeMemoryUsage(context)
                    }

                    override fun onTrimMemory(level: Int) {
                        Log.w("MemoryPressure", "内存压力等级: $level")
                        when (level) {
                            ComponentCallbacks2.TRIM_MEMORY_RUNNING_MODERATE,
                            ComponentCallbacks2.TRIM_MEMORY_RUNNING_LOW,
                            ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL -> {
                                // 应用在前台运行，但系统内存不足
                                optimizeMemoryUsage(context)
                            }
                            ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN -> {
                                // 应用UI不可见
                                clearUnnecessaryCache()
                            }
                            ComponentCallbacks2.TRIM_MEMORY_BACKGROUND,
                            ComponentCallbacks2.TRIM_MEMORY_MODERATE,
                            ComponentCallbacks2.TRIM_MEMORY_COMPLETE -> {
                                // 应用在后台，内存压力大
                                optimizeMemoryUsage(context)
                            }
                        }
                    }
                })
            }
        }
    }
}
```

---

## 11. 网络保活

通过网络连接保持应用活跃状态。

### 11.1 心跳包机制

```kotlin
class HeartbeatManager private constructor() {

    private var heartbeatTimer: Timer? = null
    private var isHeartbeatRunning = false
    private val heartbeatInterval = 30 * 1000L // 30秒

    companion object {
        @Volatile
        private var INSTANCE: HeartbeatManager? = null

        fun getInstance(): HeartbeatManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: HeartbeatManager().also { INSTANCE = it }
            }
        }
    }

    fun startHeartbeat() {
        if (isHeartbeatRunning) return

        isHeartbeatRunning = true
        heartbeatTimer = Timer("HeartbeatTimer", true)

        heartbeatTimer?.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                sendHeartbeat()
            }
        }, 0, heartbeatInterval)

        Log.d("HeartbeatManager", "心跳包机制已启动")
    }

    fun stopHeartbeat() {
        if (!isHeartbeatRunning) return

        isHeartbeatRunning = false
        heartbeatTimer?.cancel()
        heartbeatTimer = null

        Log.d("HeartbeatManager", "心跳包机制已停止")
    }

    private fun sendHeartbeat() {
        Thread {
            try {
                // 发送心跳包到服务器
                val response = performHeartbeatRequest()
                handleHeartbeatResponse(response)

                Log.d("HeartbeatManager", "心跳包发送成功")
            } catch (e: Exception) {
                Log.e("HeartbeatManager", "心跳包发送失败", e)
                handleHeartbeatFailure()
            }
        }.start()
    }

    private fun performHeartbeatRequest(): String {
        // 实现具体的网络请求
        // 这里可以使用OkHttp、Retrofit等网络库

        val url = "https://your-server.com/heartbeat"
        val connection = java.net.URL(url).openConnection()
        connection.connectTimeout = 5000
        connection.readTimeout = 5000

        return connection.getInputStream().bufferedReader().use { it.readText() }
    }

    private fun handleHeartbeatResponse(response: String) {
        // 处理服务器响应
        // 可以根据响应内容执行相应的保活策略
    }

    private fun handleHeartbeatFailure() {
        // 处理心跳失败
        // 可以尝试重连或调整心跳间隔
    }
}
```

### 11.2 长连接保活

```kotlin
class LongConnectionManager {

    private var webSocket: WebSocket? = null
    private var okHttpClient: OkHttpClient? = null
    private var isConnected = false
    private var reconnectAttempts = 0
    private val maxReconnectAttempts = 5
    private val reconnectDelay = 5000L

    fun connect() {
        if (isConnected) return

        okHttpClient = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()

        val request = Request.Builder()
            .url("wss://your-server.com/websocket")
            .build()

        webSocket = okHttpClient?.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                super.onOpen(webSocket, response)
                isConnected = true
                reconnectAttempts = 0
                Log.d("LongConnection", "WebSocket连接成功")

                // 发送认证信息
                sendAuthMessage()
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                super.onMessage(webSocket, text)
                Log.d("LongConnection", "收到消息: $text")
                handleMessage(text)
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                super.onClosing(webSocket, code, reason)
                Log.d("LongConnection", "WebSocket正在关闭: $code - $reason")
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                super.onClosed(webSocket, code, reason)
                isConnected = false
                Log.d("LongConnection", "WebSocket已关闭: $code - $reason")

                // 尝试重连
                scheduleReconnect()
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                super.onFailure(webSocket, t, response)
                isConnected = false
                Log.e("LongConnection", "WebSocket连接失败", t)

                // 尝试重连
                scheduleReconnect()
            }
        })
    }

    fun disconnect() {
        isConnected = false
        webSocket?.close(1000, "正常关闭")
        webSocket = null
        okHttpClient = null
    }

    fun sendMessage(message: String) {
        if (isConnected) {
            webSocket?.send(message)
        } else {
            Log.w("LongConnection", "连接未建立，无法发送消息")
        }
    }

    private fun sendAuthMessage() {
        val authMessage = """
            {
                "type": "auth",
                "token": "your-auth-token",
                "timestamp": ${System.currentTimeMillis()}
            }
        """.trimIndent()
        sendMessage(authMessage)
    }

    private fun handleMessage(message: String) {
        try {
            // 解析消息并执行相应操作
            // 可以根据消息类型执行不同的保活策略

            when {
                message.contains("ping") -> {
                    sendMessage("pong")
                }
                message.contains("keep_alive") -> {
                    // 执行保活逻辑
                    performKeepAliveAction()
                }
            }
        } catch (e: Exception) {
            Log.e("LongConnection", "处理消息失败", e)
        }
    }

    private fun scheduleReconnect() {
        if (reconnectAttempts >= maxReconnectAttempts) {
            Log.w("LongConnection", "重连次数已达上限，停止重连")
            return
        }

        reconnectAttempts++
        Log.d("LongConnection", "将在${reconnectDelay}ms后进行第${reconnectAttempts}次重连")

        Handler(Looper.getMainLooper()).postDelayed({
            connect()
        }, reconnectDelay)
    }

    private fun performKeepAliveAction() {
        // 执行保活相关操作
        Log.d("LongConnection", "执行保活操作")
    }
}
```

---

## 12. 最佳实践和注意事项

### 12.1 保活策略选择

```kotlin
class KeepAliveStrategySelector {

    companion object {
        fun selectOptimalStrategy(context: Context): List<KeepAliveStrategy> {
            val strategies = mutableListOf<KeepAliveStrategy>()

            // 1. 根据Android版本选择策略
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                    // Android 8.0+：推荐使用WorkManager和前台服务
                    strategies.add(KeepAliveStrategy.WORK_MANAGER)
                    strategies.add(KeepAliveStrategy.FOREGROUND_SERVICE)
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    // Android 6.0+：可以使用AlarmManager和JobScheduler
                    strategies.add(KeepAliveStrategy.ALARM_MANAGER)
                    strategies.add(KeepAliveStrategy.JOB_SCHEDULER)
                    strategies.add(KeepAliveStrategy.FOREGROUND_SERVICE)
                }
                else -> {
                    // 旧版本：可以使用更多策略
                    strategies.add(KeepAliveStrategy.DUAL_PROCESS)
                    strategies.add(KeepAliveStrategy.BROADCAST_RECEIVER)
                    strategies.add(KeepAliveStrategy.ALARM_MANAGER)
                }
            }

            // 2. 根据厂商选择策略
            if (ManufacturerHelper.needsWhitelistGuidance()) {
                strategies.add(KeepAliveStrategy.WHITELIST_GUIDE)
            }

            // 3. 根据应用类型选择策略
            if (isSystemApp(context)) {
                strategies.add(KeepAliveStrategy.SYSTEM_LEVEL)
            }

            // 4. 根据用户权限选择策略
            if (hasIgnoreBatteryOptimization(context)) {
                strategies.add(KeepAliveStrategy.IGNORE_BATTERY_OPTIMIZATION)
            }

            return strategies
        }

        private fun isSystemApp(context: Context): Boolean {
            val packageManager = context.packageManager
            val applicationInfo = packageManager.getApplicationInfo(context.packageName, 0)
            return (applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
        }

        private fun hasIgnoreBatteryOptimization(context: Context): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
                powerManager.isIgnoringBatteryOptimizations(context.packageName)
            } else {
                true
            }
        }
    }

    enum class KeepAliveStrategy {
        WORK_MANAGER,
        FOREGROUND_SERVICE,
        ALARM_MANAGER,
        JOB_SCHEDULER,
        DUAL_PROCESS,
        BROADCAST_RECEIVER,
        ACCOUNT_SYNC,
        WHITELIST_GUIDE,
        SYSTEM_LEVEL,
        IGNORE_BATTERY_OPTIMIZATION
    }
}
```

### 12.2 权限申请管理

```kotlin
class PermissionManager(private val context: Context) {

    fun requestKeepAlivePermissions(activity: Activity) {
        // 1. 请求忽略电池优化
        requestIgnoreBatteryOptimization(activity)

        // 2. 请求自启动权限（厂商相关）
        requestAutoStartPermission()

        // 3. 请求后台运行权限
        requestBackgroundPermission()

        // 4. 请求通知权限
        requestNotificationPermission()
    }

    private fun requestIgnoreBatteryOptimization(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            if (!powerManager.isIgnoringBatteryOptimizations(context.packageName)) {
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                    data = Uri.parse("package:${context.packageName}")
                }
                try {
                    activity.startActivity(intent)
                } catch (e: Exception) {
                    Log.e("PermissionManager", "请求忽略电池优化失败", e)
                }
            }
        }
    }

    private fun requestAutoStartPermission() {
        // 根据厂商跳转到相应的自启动设置页面
        WhitelistGuideManager(context).showWhitelistGuide()
    }

    private fun requestBackgroundPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ 后台位置权限
            // 这里可以根据需要请求相应的后台权限
        }
    }

    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 通知权限
            if (ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.POST_NOTIFICATIONS
                ) != PackageManager.PERMISSION_GRANTED) {

                if (context is Activity) {
                    ActivityCompat.requestPermissions(
                        context,
                        arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                        1001
                    )
                }
            }
        }
    }

    fun checkAllPermissions(): PermissionStatus {
        val status = PermissionStatus()

        // 检查电池优化
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            status.ignoreBatteryOptimization = powerManager.isIgnoringBatteryOptimizations(context.packageName)
        } else {
            status.ignoreBatteryOptimization = true
        }

        // 检查通知权限
        status.notificationPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }

        return status
    }

    data class PermissionStatus(
        var ignoreBatteryOptimization: Boolean = false,
        var notificationPermission: Boolean = false,
        var autoStartPermission: Boolean = false
    )
}
```

### 12.3 性能监控

```kotlin
class KeepAliveMonitor {

    private val metrics = mutableMapOf<String, Any>()

    fun startMonitoring(context: Context) {
        // 监控服务状态
        monitorServiceStatus(context)

        // 监控内存使用
        monitorMemoryUsage(context)

        // 监控电池使用
        monitorBatteryUsage(context)

        // 监控网络状态
        monitorNetworkStatus(context)
    }

    private fun monitorServiceStatus(context: Context) {
        Thread {
            while (true) {
                try {
                    val isKeepAliveServiceRunning = isServiceRunning(context, KeepAliveService::class.java)
                    val isMainProcessServiceRunning = isServiceRunning(context, MainProcessService::class.java)
                    val isGuardProcessServiceRunning = isServiceRunning(context, GuardProcessService::class.java)

                    metrics["keep_alive_service"] = isKeepAliveServiceRunning
                    metrics["main_process_service"] = isMainProcessServiceRunning
                    metrics["guard_process_service"] = isGuardProcessServiceRunning

                    Log.d("KeepAliveMonitor", """
                        服务状态监控:
                        保活服务: $isKeepAliveServiceRunning
                        主进程服务: $isMainProcessServiceRunning
                        守护进程服务: $isGuardProcessServiceRunning
                    """.trimIndent())

                    Thread.sleep(10000) // 10秒检查一次
                } catch (e: InterruptedException) {
                    break
                } catch (e: Exception) {
                    Log.e("KeepAliveMonitor", "服务状态监控异常", e)
                }
            }
        }.start()
    }

    private fun monitorMemoryUsage(context: Context) {
        Thread {
            while (true) {
                try {
                    val runtime = Runtime.getRuntime()
                    val usedMemory = runtime.totalMemory() - runtime.freeMemory()
                    val maxMemory = runtime.maxMemory()
                    val memoryUsagePercent = (usedMemory.toDouble() / maxMemory * 100).toInt()

                    metrics["memory_usage_percent"] = memoryUsagePercent
                    metrics["used_memory_mb"] = usedMemory / 1024 / 1024
                    metrics["max_memory_mb"] = maxMemory / 1024 / 1024

                    if (memoryUsagePercent > 80) {
                        Log.w("KeepAliveMonitor", "内存使用率过高: $memoryUsagePercent%")
                        // 触发内存优化
                        MemoryOptimizationManager.optimizeMemoryUsage(context)
                    }

                    Thread.sleep(30000) // 30秒检查一次
                } catch (e: InterruptedException) {
                    break
                } catch (e: Exception) {
                    Log.e("KeepAliveMonitor", "内存监控异常", e)
                }
            }
        }.start()
    }

    private fun monitorBatteryUsage(context: Context) {
        val batteryReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
                val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
                val batteryPercent = (level.toFloat() / scale * 100).toInt()

                metrics["battery_percent"] = batteryPercent

                Log.d("KeepAliveMonitor", "电池电量: $batteryPercent%")

                // 低电量时调整保活策略
                if (batteryPercent < 20) {
                    adjustKeepAliveStrategy(context, true)
                } else if (batteryPercent > 50) {
                    adjustKeepAliveStrategy(context, false)
                }
            }
        }

        val filter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        context.registerReceiver(batteryReceiver, filter)
    }

    private fun monitorNetworkStatus(context: Context) {
        val networkReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val isConnected = isNetworkAvailable(context)
                metrics["network_connected"] = isConnected

                Log.d("KeepAliveMonitor", "网络状态: ${if (isConnected) "已连接" else "未连接"}")

                if (isConnected) {
                    // 网络恢复时重启网络相关的保活机制
                    HeartbeatManager.getInstance().startHeartbeat()
                } else {
                    // 网络断开时停止网络相关的保活机制
                    HeartbeatManager.getInstance().stopHeartbeat()
                }
            }
        }

        val filter = IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
        context.registerReceiver(networkReceiver, filter)
    }

    private fun adjustKeepAliveStrategy(context: Context, isLowBattery: Boolean) {
        if (isLowBattery) {
            // 低电量时采用节能策略
            Log.d("KeepAliveMonitor", "低电量模式，调整保活策略")

            // 降低心跳频率
            HeartbeatManager.getInstance().stopHeartbeat()

            // 减少WorkManager任务频率
            // 可以重新配置WorkManager的约束条件

        } else {
            // 正常电量时恢复正常策略
            Log.d("KeepAliveMonitor", "正常电量，恢复保活策略")

            // 恢复心跳
            HeartbeatManager.getInstance().startHeartbeat()

            // 恢复正常的保活机制
            KeepAliveManager.getInstance(context).startKeepAlive()
        }
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }

    private fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.isConnected == true
        }
    }

    fun getMetrics(): Map<String, Any> {
        return metrics.toMap()
    }
}
```

---

## 13. 完整的保活方案示例

### 13.1 Application初始化

```kotlin
class KeepAliveApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        // 初始化保活系统
        initKeepAliveSystem()

        // 注册内存压力回调
        MemoryOptimizationManager.registerMemoryPressureCallback(this)

        // 启动监控
        KeepAliveMonitor().startMonitoring(this)
    }

    private fun initKeepAliveSystem() {
        // 1. 启动保活管理器
        KeepAliveManager.getInstance(this).startKeepAlive()

        // 2. 注册系统广播
        BroadcastManager(this).registerBroadcasts()

        // 3. 启动心跳机制
        HeartbeatManager.getInstance().startHeartbeat()

        // 4. 设置进程优先级
        ProcessPriorityManager.setHighPriority()

        Log.d("KeepAliveApplication", "保活系统初始化完成")
    }
}
```

### 13.2 MainActivity集成

```kotlin
class MainActivity : AppCompatActivity() {

    private lateinit var permissionManager: PermissionManager
    private lateinit var whitelistGuideManager: WhitelistGuideManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        permissionManager = PermissionManager(this)
        whitelistGuideManager = WhitelistGuideManager(this)

        // 检查并请求权限
        checkAndRequestPermissions()

        // 显示白名单引导
        showWhitelistGuideIfNeeded()

        // 启动保活服务
        startKeepAliveServices()
    }

    private fun checkAndRequestPermissions() {
        val status = permissionManager.checkAllPermissions()

        if (!status.ignoreBatteryOptimization || !status.notificationPermission) {
            permissionManager.requestKeepAlivePermissions(this)
        }
    }

    private fun showWhitelistGuideIfNeeded() {
        if (ManufacturerHelper.needsWhitelistGuidance()) {
            whitelistGuideManager.showWhitelistGuide()
        }
    }

    private fun startKeepAliveServices() {
        // 启动前台服务
        val intent = Intent(this, KeepAliveService::class.java)
        startForegroundService(intent)

        // 启动保活管理器
        KeepAliveManager.getInstance(this).startKeepAlive()
    }

    override fun onDestroy() {
        super.onDestroy()

        // 注意：不要在这里停止保活机制
        // 保活机制应该在应用真正退出时才停止
    }
}
```

---

## 14. 总结和建议

### 14.1 保活技术总结

1. **WorkManager**: 现代化的后台任务处理方案，推荐优先使用
2. **AlarmManager**: 定时任务保活，适用于周期性任务
3. **前台服务**: 最可靠的保活方式，但会显示通知
4. **双进程守护**: 传统保活方法，在新版本Android中效果有限
5. **广播拉活**: 通过系统广播实现拉活，受系统限制较多
6. **账户同步**: 相对隐蔽的保活方式，但实现复杂
7. **厂商适配**: 针对不同厂商的特殊处理，提高保活成功率

### 14.2 最佳实践建议

1. **合理选择策略**: 根据Android版本和厂商选择合适的保活策略
2. **用户体验优先**: 不要过度保活，影响用户体验和设备性能
3. **权限申请**: 主动引导用户授予必要的权限
4. **性能监控**: 实时监控保活效果和资源使用情况
5. **优雅降级**: 在保活失败时有备用方案
6. **遵守规范**: 遵循Google和各厂商的开发规范

### 14.3 注意事项

1. **避免过度保活**: 不要为了保活而牺牲用户体验
2. **内存管理**: 注意内存使用，避免因内存不足被系统杀死
3. **电量优化**: 在低电量时调整保活策略
4. **版本适配**: 针对不同Android版本采用不同策略
5. **厂商测试**: 在主流厂商设备上充分测试保活效果

通过合理组合使用这些保活技术，可以在大多数情况下实现应用的稳定运行。但需要注意的是，随着Android系统的不断更新和厂商定制的变化，保活策略也需要持续优化和调整。
