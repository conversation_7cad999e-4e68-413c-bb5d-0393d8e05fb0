# 12 - Android应用保活和拉活技术详解

## 概述

应用保活（Keep Alive）和拉活（Revival）是Android开发中的重要技术，用于确保应用在后台能够持续运行或在被杀死后能够重新启动。随着Android系统版本的不断更新，Google对后台应用的限制越来越严格，传统的保活方法逐渐失效，需要采用更加规范和系统友好的方式。

---

## 1. 保活技术分类

### 1.1 系统级保活
- **前台服务 (Foreground Service)**
- **JobScheduler / JobIntentService**
- **WorkManager (推荐)**
- **AlarmManager**

### 1.2 应用级保活
- **双进程守护**
- **Service自启动**
- **广播拉活**
- **账户同步拉活**

### 1.3 厂商级保活
- **白名单机制**
- **自启动管理**
- **后台应用保护**

---

## 2. WorkManager - 现代化后台任务解决方案

WorkManager是Google推荐的后台任务处理框架，能够在各种系统约束下可靠地执行任务。

### 2.1 WorkManager基本使用

#### 添加依赖
```gradle
dependencies {
    implementation "androidx.work:work-runtime:2.8.1"
    // 可选：RxJava2支持
    implementation "androidx.work:work-rxjava2:2.8.1"
    // 可选：Kotlin协程支持
    implementation "androidx.work:work-runtime-ktx:2.8.1"
}
```

#### 创建Worker
```kotlin
class KeepAliveWorker(context: Context, params: WorkerParameters) : Worker(context, params) {
    
    override fun doWork(): Result {
        return try {
            // 执行保活逻辑
            performKeepAliveTask()
            
            // 发送心跳包
            sendHeartbeat()
            
            // 检查应用状态
            checkAppStatus()
            
            Result.success()
        } catch (e: Exception) {
            Log.e("KeepAliveWorker", "保活任务执行失败", e)
            Result.retry()
        }
    }
    
    private fun performKeepAliveTask() {
        // 实现具体的保活逻辑
        Log.d("KeepAliveWorker", "执行保活任务: ${System.currentTimeMillis()}")
        
        // 可以在这里执行：
        // 1. 网络请求保持连接
        // 2. 数据同步
        // 3. 状态上报
        // 4. 缓存清理等
    }
    
    private fun sendHeartbeat() {
        // 发送心跳包到服务器
        // 实现网络保活逻辑
    }
    
    private fun checkAppStatus() {
        // 检查应用各模块状态
        // 必要时重新初始化
    }
}
```

#### 配置和启动WorkManager
```kotlin
class KeepAliveManager {
    
    companion object {
        private const val KEEP_ALIVE_WORK_NAME = "keep_alive_work"
        private const val PERIODIC_INTERVAL_MINUTES = 15L
    }
    
    fun startKeepAliveWork(context: Context) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED) // 需要网络连接
            .setRequiresBatteryNotLow(true) // 电量不能过低
            .setRequiresCharging(false) // 不要求充电状态
            .setRequiresDeviceIdle(false) // 不要求设备空闲
            .build()
        
        val keepAliveRequest = PeriodicWorkRequestBuilder<KeepAliveWorker>(
            PERIODIC_INTERVAL_MINUTES, TimeUnit.MINUTES
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.LINEAR,
                PeriodicWorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .build()
        
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            KEEP_ALIVE_WORK_NAME,
            ExistingPeriodicWorkPolicy.KEEP,
            keepAliveRequest
        )
    }
    
    fun stopKeepAliveWork(context: Context) {
        WorkManager.getInstance(context).cancelUniqueWork(KEEP_ALIVE_WORK_NAME)
    }
    
    fun getWorkStatus(context: Context): LiveData<List<WorkInfo>> {
        return WorkManager.getInstance(context).getWorkInfosForUniqueWorkLiveData(KEEP_ALIVE_WORK_NAME)
    }
}
```

### 2.2 WorkManager高级用法

#### 链式任务执行
```kotlin
class ChainedKeepAliveManager {
    
    fun startChainedWork(context: Context) {
        val initWork = OneTimeWorkRequestBuilder<InitializationWorker>().build()
        val keepAliveWork = OneTimeWorkRequestBuilder<KeepAliveWorker>().build()
        val cleanupWork = OneTimeWorkRequestBuilder<CleanupWorker>().build()
        
        WorkManager.getInstance(context)
            .beginWith(initWork)
            .then(keepAliveWork)
            .then(cleanupWork)
            .enqueue()
    }
}

class InitializationWorker(context: Context, params: WorkerParameters) : Worker(context, params) {
    override fun doWork(): Result {
        // 初始化工作
        return Result.success()
    }
}

class CleanupWorker(context: Context, params: WorkerParameters) : Worker(context, params) {
    override fun doWork(): Result {
        // 清理工作
        return Result.success()
    }
}
```

#### 带数据传递的Worker
```kotlin
class DataKeepAliveWorker(context: Context, params: WorkerParameters) : Worker(context, params) {
    
    override fun doWork(): Result {
        val userId = inputData.getString("user_id") ?: return Result.failure()
        val taskType = inputData.getString("task_type") ?: "default"
        
        return try {
            performTaskForUser(userId, taskType)
            
            val outputData = Data.Builder()
                .putString("result", "success")
                .putLong("timestamp", System.currentTimeMillis())
                .build()
                
            Result.success(outputData)
        } catch (e: Exception) {
            Result.failure()
        }
    }
    
    private fun performTaskForUser(userId: String, taskType: String) {
        // 执行特定用户的保活任务
    }
}

// 使用方式
fun startDataWork(context: Context, userId: String) {
    val inputData = Data.Builder()
        .putString("user_id", userId)
        .putString("task_type", "keep_alive")
        .build()
    
    val workRequest = OneTimeWorkRequestBuilder<DataKeepAliveWorker>()
        .setInputData(inputData)
        .build()
    
    WorkManager.getInstance(context).enqueue(workRequest)
}
```

---

## 3. AlarmManager - 定时任务保活

AlarmManager是Android系统提供的定时任务服务，即使在应用被杀死的情况下也能触发。

### 3.1 AlarmManager基本使用

#### 创建AlarmReceiver
```kotlin
class KeepAliveAlarmReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            ACTION_KEEP_ALIVE -> {
                handleKeepAlive(context)
            }
            ACTION_RESTART_SERVICE -> {
                restartService(context)
            }
        }
    }
    
    private fun handleKeepAlive(context: Context) {
        Log.d("KeepAliveAlarm", "执行保活逻辑")
        
        // 检查服务是否运行
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            // 重启服务
            val serviceIntent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(serviceIntent)
        }
        
        // 重新设置下一次闹钟
        scheduleNextAlarm(context)
    }
    
    private fun restartService(context: Context) {
        val serviceIntent = Intent(context, KeepAliveService::class.java)
        context.startForegroundService(serviceIntent)
    }
    
    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any { 
            it.service.className == serviceClass.name 
        }
    }
    
    private fun scheduleNextAlarm(context: Context) {
        AlarmManagerHelper.setKeepAliveAlarm(context)
    }
    
    companion object {
        const val ACTION_KEEP_ALIVE = "com.example.KEEP_ALIVE"
        const val ACTION_RESTART_SERVICE = "com.example.RESTART_SERVICE"
    }
}
```

#### AlarmManager工具类
```kotlin
class AlarmManagerHelper {
    
    companion object {
        private const val ALARM_INTERVAL = 30 * 1000L // 30秒
        private const val KEEP_ALIVE_REQUEST_CODE = 1001
        
        fun setKeepAliveAlarm(context: Context) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            
            val intent = Intent(context, KeepAliveAlarmReceiver::class.java).apply {
                action = KeepAliveAlarmReceiver.ACTION_KEEP_ALIVE
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                KEEP_ALIVE_REQUEST_CODE,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            val triggerTime = System.currentTimeMillis() + ALARM_INTERVAL
            
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    // Android 6.0+ 使用 setExactAndAllowWhileIdle
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT -> {
                    // Android 4.4+ 使用 setExact
                    alarmManager.setExact(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                }
                else -> {
                    // 旧版本使用 set
                    alarmManager.set(
                        AlarmManager.RTC_WAKEUP,
                        triggerTime,
                        pendingIntent
                    )
                }
            }
        }
        
        fun cancelKeepAliveAlarm(context: Context) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val intent = Intent(context, KeepAliveAlarmReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                KEEP_ALIVE_REQUEST_CODE,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            alarmManager.cancel(pendingIntent)
        }
    }
}
```

### 3.2 AlarmManager高级用法

#### 智能间隔调整
```kotlin
class SmartAlarmManager {
    
    private var failureCount = 0
    private val baseInterval = 30 * 1000L // 基础间隔30秒
    private val maxInterval = 5 * 60 * 1000L // 最大间隔5分钟
    
    fun setSmartAlarm(context: Context, isSuccess: Boolean = true) {
        if (isSuccess) {
            failureCount = 0
        } else {
            failureCount++
        }
        
        val interval = calculateInterval()
        setAlarmWithInterval(context, interval)
    }
    
    private fun calculateInterval(): Long {
        // 指数退避算法
        val backoffMultiplier = Math.pow(2.0, failureCount.toDouble()).toLong()
        val calculatedInterval = baseInterval * backoffMultiplier
        return Math.min(calculatedInterval, maxInterval)
    }
    
    private fun setAlarmWithInterval(context: Context, interval: Long) {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val intent = Intent(context, KeepAliveAlarmReceiver::class.java)
        val pendingIntent = PendingIntent.getBroadcast(
            context, 1001, intent, 
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val triggerTime = System.currentTimeMillis() + interval
        alarmManager.setExactAndAllowWhileIdle(
            AlarmManager.RTC_WAKEUP,
            triggerTime,
            pendingIntent
        )
    }
}
```

---

## 4. 前台服务保活

前台服务是最可靠的保活方式之一，因为它对用户可见，系统不会轻易杀死。

### 4.1 前台服务实现

```kotlin
class KeepAliveService : Service() {
    
    private val serviceId = 1001
    private val channelId = "keep_alive_channel"
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        startForeground(serviceId, createNotification())
        
        // 启动保活逻辑
        startKeepAliveLogic()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                "应用保活服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "保持应用在后台运行"
                setShowBadge(false)
                setSound(null, null)
                enableVibration(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent, 
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, channelId)
            .setContentTitle("应用正在后台运行")
            .setContentText("点击返回应用")
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setOngoing(true) // 不可滑动删除
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }
    
    private fun startKeepAliveLogic() {
        // 启动定时任务
        AlarmManagerHelper.setKeepAliveAlarm(this)
        
        // 启动WorkManager任务
        KeepAliveManager().startKeepAliveWork(this)
    }
}
```

---

## 5. 双进程守护保活

通过创建两个进程相互监听，当一个进程被杀死时，另一个进程负责拉起。

### 5.1 主进程服务
```kotlin
class MainProcessService : Service() {
    
    private var isServiceRunning = true
    private lateinit var guardConnection: ServiceConnection
    
    override fun onCreate() {
        super.onCreate()
        startGuardService()
        startMonitorThread()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun startGuardService() {
        val intent = Intent(this, GuardProcessService::class.java)
        guardConnection = object : ServiceConnection {
            override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
                Log.d("MainProcess", "守护进程连接成功")
            }
            
            override fun onServiceDisconnected(name: ComponentName?) {
                Log.d("MainProcess", "守护进程断开连接，尝试重启")
                startGuardService()
            }
        }
        bindService(intent, guardConnection, Context.BIND_AUTO_CREATE)
    }
    
    private fun startMonitorThread() {
        Thread {
            while (isServiceRunning) {
                try {
                    // 检查守护进程是否存在
                    if (!isGuardProcessRunning()) {
                        restartGuardProcess()
                    }
                    Thread.sleep(5000) // 5秒检查一次
                } catch (e: InterruptedException) {
                    break
                }
            }
        }.start()
    }
    
    private fun isGuardProcessRunning(): Boolean {
        val manager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.runningAppProcesses?.any { 
            it.processName.contains(":guard") 
        } ?: false
    }
    
    private fun restartGuardProcess() {
        val intent = Intent(this, GuardProcessService::class.java)
        startService(intent)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        isServiceRunning = false
        try {
            unbindService(guardConnection)
        } catch (e: Exception) {
            // 忽略异常
        }
    }
}
```

### 5.2 守护进程服务
```kotlin
class GuardProcessService : Service() {
    
    private var isServiceRunning = true
    
    override fun onCreate() {
        super.onCreate()
        startMonitorThread()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = GuardBinder()
    
    private fun startMonitorThread() {
        Thread {
            while (isServiceRunning) {
                try {
                    // 检查主进程是否存在
                    if (!isMainProcessRunning()) {
                        restartMainProcess()
                    }
                    Thread.sleep(5000) // 5秒检查一次
                } catch (e: InterruptedException) {
                    break
                }
            }
        }.start()
    }
    
    private fun isMainProcessRunning(): Boolean {
        val manager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val packageName = packageName
        return manager.runningAppProcesses?.any { 
            it.processName == packageName 
        } ?: false
    }
    
    private fun restartMainProcess() {
        try {
            val intent = Intent(this, MainProcessService::class.java)
            startService(intent)
            
            // 也可以尝试启动Activity来拉起主进程
            val activityIntent = Intent(this, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            startActivity(activityIntent)
        } catch (e: Exception) {
            Log.e("GuardProcess", "重启主进程失败", e)
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        isServiceRunning = false
    }
    
    inner class GuardBinder : Binder() {
        fun getService(): GuardProcessService = this@GuardProcessService
    }
}
```

### 5.3 AndroidManifest.xml配置
```xml
<!-- 主进程服务 -->
<service
    android:name=".service.MainProcessService"
    android:enabled="true"
    android:exported="false" />

<!-- 守护进程服务 -->
<service
    android:name=".service.GuardProcessService"
    android:enabled="true"
    android:exported="false"
    android:process=":guard" />

<!-- 保活广播接收器 -->
<receiver
    android:name=".receiver.KeepAliveAlarmReceiver"
    android:enabled="true"
    android:exported="false">
    <intent-filter>
        <action android:name="com.example.KEEP_ALIVE" />
        <action android:name="com.example.RESTART_SERVICE" />
    </intent-filter>
</receiver>
```

---

## 6. 保活管理器

创建一个统一的保活管理器来协调各种保活策略。

```kotlin
class KeepAliveManager private constructor(private val context: Context) {
    
    private val sharedPreferences = context.getSharedPreferences("keep_alive", Context.MODE_PRIVATE)
    private var isKeepAliveEnabled = false
    
    companion object {
        @Volatile
        private var INSTANCE: KeepAliveManager? = null
        
        fun getInstance(context: Context): KeepAliveManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: KeepAliveManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    fun startKeepAlive() {
        if (isKeepAliveEnabled) return
        
        isKeepAliveEnabled = true
        sharedPreferences.edit().putBoolean("keep_alive_enabled", true).apply()
        
        // 1. 启动前台服务
        startForegroundService()
        
        // 2. 启动WorkManager任务
        startWorkManagerTask()
        
        // 3. 设置AlarmManager
        setAlarmManager()
        
        // 4. 启动双进程守护
        startDualProcessGuard()
        
        Log.d("KeepAliveManager", "保活机制已启动")
    }
    
    fun stopKeepAlive() {
        if (!isKeepAliveEnabled) return
        
        isKeepAliveEnabled = false
        sharedPreferences.edit().putBoolean("keep_alive_enabled", false).apply()
        
        // 停止所有保活机制
        stopForegroundService()
        stopWorkManagerTask()
        cancelAlarmManager()
        stopDualProcessGuard()
        
        Log.d("KeepAliveManager", "保活机制已停止")
    }
    
    private fun startForegroundService() {
        val intent = Intent(context, KeepAliveService::class.java)
        context.startForegroundService(intent)
    }
    
    private fun stopForegroundService() {
        val intent = Intent(context, KeepAliveService::class.java)
        context.stopService(intent)
    }
    
    private fun startWorkManagerTask() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()
        
        val workRequest = PeriodicWorkRequestBuilder<KeepAliveWorker>(15, TimeUnit.MINUTES)
            .setConstraints(constraints)
            .build()
        
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            "keep_alive_work",
            ExistingPeriodicWorkPolicy.KEEP,
            workRequest
        )
    }
    
    private fun stopWorkManagerTask() {
        WorkManager.getInstance(context).cancelUniqueWork("keep_alive_work")
    }
    
    private fun setAlarmManager() {
        AlarmManagerHelper.setKeepAliveAlarm(context)
    }
    
    private fun cancelAlarmManager() {
        AlarmManagerHelper.cancelKeepAliveAlarm(context)
    }
    
    private fun startDualProcessGuard() {
        // 启动主进程服务
        val mainIntent = Intent(context, MainProcessService::class.java)
        context.startService(mainIntent)
        
        // 启动守护进程服务
        val guardIntent = Intent(context, GuardProcessService::class.java)
        context.startService(guardIntent)
    }
    
    private fun stopDualProcessGuard() {
        context.stopService(Intent(context, MainProcessService::class.java))
        context.stopService(Intent(context, GuardProcessService::class.java))
    }
    
    fun isKeepAliveRunning(): Boolean {
        return sharedPreferences.getBoolean("keep_alive_enabled", false)
    }
}

---

## 7. 广播拉活机制

通过监听系统广播来实现应用拉活，虽然Android 8.0+限制了静态广播，但仍有一些有效的方法。

### 7.1 系统广播监听

```kotlin
class SystemBroadcastReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        Log.d("SystemBroadcast", "收到系统广播: ${intent.action}")

        when (intent.action) {
            Intent.ACTION_SCREEN_ON -> {
                handleScreenOn(context)
            }
            Intent.ACTION_SCREEN_OFF -> {
                handleScreenOff(context)
            }
            Intent.ACTION_USER_PRESENT -> {
                handleUserPresent(context)
            }
            ConnectivityManager.CONNECTIVITY_ACTION -> {
                handleNetworkChange(context)
            }
            Intent.ACTION_POWER_CONNECTED -> {
                handlePowerConnected(context)
            }
            Intent.ACTION_POWER_DISCONNECTED -> {
                handlePowerDisconnected(context)
            }
        }
    }

    private fun handleScreenOn(context: Context) {
        // 屏幕点亮时的拉活逻辑
        restartServicesIfNeeded(context)
    }

    private fun handleScreenOff(context: Context) {
        // 屏幕关闭时的保活逻辑
        ensureServicesRunning(context)
    }

    private fun handleUserPresent(context: Context) {
        // 用户解锁时的拉活逻辑
        restartServicesIfNeeded(context)

        // 可以在这里启动一个透明Activity来拉活
        startTransparentActivity(context)
    }

    private fun handleNetworkChange(context: Context) {
        // 网络状态变化时的拉活逻辑
        if (isNetworkAvailable(context)) {
            restartServicesIfNeeded(context)
        }
    }

    private fun handlePowerConnected(context: Context) {
        // 充电时的拉活逻辑
        restartServicesIfNeeded(context)
    }

    private fun handlePowerDisconnected(context: Context) {
        // 断开充电时的保活逻辑
        ensureServicesRunning(context)
    }

    private fun restartServicesIfNeeded(context: Context) {
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            val intent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(intent)
        }

        // 重新设置AlarmManager
        AlarmManagerHelper.setKeepAliveAlarm(context)
    }

    private fun ensureServicesRunning(context: Context) {
        // 确保关键服务正在运行
        restartServicesIfNeeded(context)
    }

    private fun startTransparentActivity(context: Context) {
        val intent = Intent(context, TransparentActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_NO_HISTORY
        }
        context.startActivity(intent)
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }

    private fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.isConnected == true
        }
    }
}
```

### 7.2 动态注册广播

```kotlin
class BroadcastManager(private val context: Context) {

    private var systemBroadcastReceiver: SystemBroadcastReceiver? = null
    private var isRegistered = false

    fun registerBroadcasts() {
        if (isRegistered) return

        systemBroadcastReceiver = SystemBroadcastReceiver()
        val intentFilter = IntentFilter().apply {
            // 屏幕相关
            addAction(Intent.ACTION_SCREEN_ON)
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_USER_PRESENT)

            // 网络相关
            addAction(ConnectivityManager.CONNECTIVITY_ACTION)

            // 电源相关
            addAction(Intent.ACTION_POWER_CONNECTED)
            addAction(Intent.ACTION_POWER_DISCONNECTED)

            // 其他系统事件
            addAction(Intent.ACTION_BOOT_COMPLETED)
            addAction(Intent.ACTION_MY_PACKAGE_REPLACED)
            addAction(Intent.ACTION_PACKAGE_REPLACED)
            addDataScheme("package")
        }

        context.registerReceiver(systemBroadcastReceiver, intentFilter)
        isRegistered = true

        Log.d("BroadcastManager", "系统广播已注册")
    }

    fun unregisterBroadcasts() {
        if (!isRegistered || systemBroadcastReceiver == null) return

        try {
            context.unregisterReceiver(systemBroadcastReceiver)
            isRegistered = false
            systemBroadcastReceiver = null
            Log.d("BroadcastManager", "系统广播已注销")
        } catch (e: Exception) {
            Log.e("BroadcastManager", "注销广播失败", e)
        }
    }
}
```

### 7.3 透明Activity拉活

```kotlin
class TransparentActivity : Activity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置透明主题
        window.setFlags(
            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
        )

        // 执行拉活逻辑
        performReviveLogic()

        // 立即关闭Activity
        finish()
    }

    private fun performReviveLogic() {
        // 启动保活服务
        val intent = Intent(this, KeepAliveService::class.java)
        startForegroundService(intent)

        // 启动保活管理器
        KeepAliveManager.getInstance(this).startKeepAlive()

        Log.d("TransparentActivity", "透明Activity拉活完成")
    }

    override fun onResume() {
        super.onResume()
        // 确保Activity不可见
        moveTaskToBack(true)
    }
}
```

---

## 8. 账户同步拉活

通过Android的账户同步机制来实现应用拉活，这是一种相对隐蔽的方法。

### 8.1 创建同步适配器

```kotlin
class KeepAliveSyncAdapter(
    context: Context,
    autoInitialize: Boolean
) : AbstractThreadedSyncAdapter(context, autoInitialize) {

    override fun onPerformSync(
        account: Account?,
        extras: Bundle?,
        authority: String?,
        provider: ContentProviderClient?,
        syncResult: SyncResult?
    ) {
        Log.d("SyncAdapter", "执行同步任务")

        try {
            // 执行保活逻辑
            performKeepAliveSync()

            // 启动服务
            restartServices()

            // 设置下次同步
            scheduleNextSync(account, authority)

        } catch (e: Exception) {
            Log.e("SyncAdapter", "同步任务执行失败", e)
            syncResult?.stats?.numIoExceptions++
        }
    }

    private fun performKeepAliveSync() {
        // 模拟数据同步
        Thread.sleep(1000)

        // 检查应用状态
        checkAppStatus()

        // 发送心跳
        sendHeartbeat()
    }

    private fun restartServices() {
        val context = context
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            val intent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(intent)
        }
    }

    private fun scheduleNextSync(account: Account?, authority: String?) {
        if (account != null && authority != null) {
            // 设置定期同步
            ContentResolver.addPeriodicSync(
                account,
                authority,
                Bundle.EMPTY,
                60 * 15 // 15分钟同步一次
            )
        }
    }

    private fun checkAppStatus() {
        // 检查应用各模块状态
    }

    private fun sendHeartbeat() {
        // 发送心跳包
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }
}
```

### 8.2 同步服务

```kotlin
class KeepAliveSyncService : Service() {

    private var syncAdapter: KeepAliveSyncAdapter? = null

    override fun onCreate() {
        super.onCreate()
        synchronized(this) {
            if (syncAdapter == null) {
                syncAdapter = KeepAliveSyncAdapter(applicationContext, true)
            }
        }
    }

    override fun onBind(intent: Intent?): IBinder? {
        return syncAdapter?.syncAdapterBinder
    }
}
```

### 8.3 认证器

```kotlin
class KeepAliveAuthenticator(context: Context) : AbstractAccountAuthenticator(context) {

    override fun editProperties(response: AccountAuthenticatorResponse?, accountType: String?): Bundle? {
        return null
    }

    override fun addAccount(
        response: AccountAuthenticatorResponse?,
        accountType: String?,
        authTokenType: String?,
        requiredFeatures: Array<out String>?,
        options: Bundle?
    ): Bundle? {
        return null
    }

    override fun confirmCredentials(
        response: AccountAuthenticatorResponse?,
        account: Account?,
        options: Bundle?
    ): Bundle? {
        return null
    }

    override fun getAuthToken(
        response: AccountAuthenticatorResponse?,
        account: Account?,
        authTokenType: String?,
        options: Bundle?
    ): Bundle? {
        return null
    }

    override fun getAuthTokenLabel(authTokenType: String?): String? {
        return null
    }

    override fun updateCredentials(
        response: AccountAuthenticatorResponse?,
        account: Account?,
        authTokenType: String?,
        options: Bundle?
    ): Bundle? {
        return null
    }

    override fun hasFeatures(
        response: AccountAuthenticatorResponse?,
        account: Account?,
        features: Array<out String>?
    ): Bundle? {
        return null
    }
}

class KeepAliveAuthenticatorService : Service() {

    private var authenticator: KeepAliveAuthenticator? = null

    override fun onCreate() {
        super.onCreate()
        authenticator = KeepAliveAuthenticator(this)
    }

    override fun onBind(intent: Intent?): IBinder? {
        return authenticator?.iBinder
    }
}
```

### 8.4 内容提供者

```kotlin
class KeepAliveContentProvider : ContentProvider() {

    companion object {
        const val AUTHORITY = "com.example.keepalive.provider"
        private val uriMatcher = UriMatcher(UriMatcher.NO_MATCH)

        init {
            uriMatcher.addURI(AUTHORITY, "data", 1)
        }
    }

    override fun onCreate(): Boolean {
        return true
    }

    override fun query(
        uri: Uri,
        projection: Array<out String>?,
        selection: String?,
        selectionArgs: Array<out String>?,
        sortOrder: String?
    ): Cursor? {
        // 在查询时执行保活逻辑
        performKeepAliveCheck()
        return null
    }

    override fun getType(uri: Uri): String? {
        return null
    }

    override fun insert(uri: Uri, values: ContentValues?): Uri? {
        return null
    }

    override fun delete(uri: Uri, selection: String?, selectionArgs: Array<out String>?): Int {
        return 0
    }

    override fun update(
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<out String>?
    ): Int {
        return 0
    }

    private fun performKeepAliveCheck() {
        val context = context ?: return

        // 检查服务状态
        if (!isServiceRunning(context, KeepAliveService::class.java)) {
            val intent = Intent(context, KeepAliveService::class.java)
            context.startForegroundService(intent)
        }
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }
}
```

---

## 9. 厂商适配和白名单

不同厂商对后台应用有不同的限制策略，需要针对性适配。

### 9.1 厂商检测工具

```kotlin
class ManufacturerHelper {

    companion object {
        fun getManufacturer(): String {
            return Build.MANUFACTURER.lowercase()
        }

        fun isHuawei(): Boolean {
            return getManufacturer().contains("huawei")
        }

        fun isXiaomi(): Boolean {
            return getManufacturer().contains("xiaomi")
        }

        fun isOppo(): Boolean {
            return getManufacturer().contains("oppo")
        }

        fun isVivo(): Boolean {
            return getManufacturer().contains("vivo")
        }

        fun isSamsung(): Boolean {
            return getManufacturer().contains("samsung")
        }

        fun isOnePlus(): Boolean {
            return getManufacturer().contains("oneplus")
        }

        fun isMeizu(): Boolean {
            return getManufacturer().contains("meizu")
        }

        fun needsWhitelistGuidance(): Boolean {
            return isHuawei() || isXiaomi() || isOppo() || isVivo() || isOnePlus() || isMeizu()
        }
    }
}
```

### 9.2 白名单引导

```kotlin
class WhitelistGuideManager(private val context: Context) {

    fun showWhitelistGuide() {
        if (!ManufacturerHelper.needsWhitelistGuidance()) {
            return
        }

        val manufacturer = ManufacturerHelper.getManufacturer()
        val guideMessage = getGuideMessage(manufacturer)

        AlertDialog.Builder(context)
            .setTitle("应用保活设置")
            .setMessage(guideMessage)
            .setPositiveButton("去设置") { _, _ ->
                openWhitelistSettings()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun getGuideMessage(manufacturer: String): String {
        return when {
            ManufacturerHelper.isHuawei() -> {
                "为了确保应用正常运行，请将本应用加入以下白名单：\n" +
                "1. 设置 → 电池 → 启动管理 → 手动管理\n" +
                "2. 设置 → 应用 → 权限管理 → 自启动管理\n" +
                "3. 手机管家 → 电池管理 → 受保护应用"
            }
            ManufacturerHelper.isXiaomi() -> {
                "为了确保应用正常运行，请进行以下设置：\n" +
                "1. 设置 → 应用设置 → 授权管理 → 自启动管理\n" +
                "2. 设置 → 电量和性能 → 应用配置 → 无限制\n" +
                "3. 安全中心 → 电量 → 应用智能省电 → 无限制"
            }
            ManufacturerHelper.isOppo() -> {
                "为了确保应用正常运行，请进行以下设置：\n" +
                "1. 设置 → 电池 → 耗电保护 → 允许后台运行\n" +
                "2. 设置 → 应用管理 → 自启动管理\n" +
                "3. 手机管家 → 权限隐私 → 自启动管理"
            }
            ManufacturerHelper.isVivo() -> {
                "为了确保应用正常运行，请进行以下设置：\n" +
                "1. 设置 → 电池 → 后台耗电管理 → 允许后台高耗电\n" +
                "2. 设置 → 更多设置 → 权限管理 → 自启动\n" +
                "3. i管家 → 应用管理 → 自启动管理"
            }
            else -> {
                "为了确保应用正常运行，请将本应用加入系统白名单，允许自启动和后台运行。"
            }
        }
    }

    private fun openWhitelistSettings() {
        try {
            when {
                ManufacturerHelper.isHuawei() -> openHuaweiSettings()
                ManufacturerHelper.isXiaomi() -> openXiaomiSettings()
                ManufacturerHelper.isOppo() -> openOppoSettings()
                ManufacturerHelper.isVivo() -> openVivoSettings()
                else -> openGeneralSettings()
            }
        } catch (e: Exception) {
            Log.e("WhitelistGuide", "打开设置页面失败", e)
            openGeneralSettings()
        }
    }

    private fun openHuaweiSettings() {
        val intent = Intent().apply {
            component = ComponentName(
                "com.huawei.systemmanager",
                "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity"
            )
        }
        context.startActivity(intent)
    }

    private fun openXiaomiSettings() {
        val intent = Intent().apply {
            component = ComponentName(
                "com.miui.securitycenter",
                "com.miui.permcenter.autostart.AutoStartManagementActivity"
            )
        }
        context.startActivity(intent)
    }

    private fun openOppoSettings() {
        val intent = Intent().apply {
            component = ComponentName(
                "com.coloros.safecenter",
                "com.coloros.safecenter.permission.startup.StartupAppListActivity"
            )
        }
        context.startActivity(intent)
    }

    private fun openVivoSettings() {
        val intent = Intent().apply {
            component = ComponentName(
                "com.vivo.permissionmanager",
                "com.vivo.permissionmanager.activity.BgStartUpManagerActivity"
            )
        }
        context.startActivity(intent)
    }

    private fun openGeneralSettings() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", context.packageName, null)
        }
        context.startActivity(intent)
    }
}

---

## 10. 进程优先级管理

通过提高进程优先级来降低被系统杀死的概率。

### 10.1 进程优先级提升

```kotlin
class ProcessPriorityManager {

    companion object {
        fun setProcessPriority(priority: Int) {
            try {
                Process.setThreadPriority(priority)
                Log.d("ProcessPriority", "进程优先级设置为: $priority")
            } catch (e: Exception) {
                Log.e("ProcessPriority", "设置进程优先级失败", e)
            }
        }

        fun setHighPriority() {
            setProcessPriority(Process.THREAD_PRIORITY_URGENT_AUDIO)
        }

        fun setNormalPriority() {
            setProcessPriority(Process.THREAD_PRIORITY_DEFAULT)
        }

        fun setLowPriority() {
            setProcessPriority(Process.THREAD_PRIORITY_BACKGROUND)
        }

        fun getCurrentPriority(): Int {
            return Process.getThreadPriority(Process.myTid())
        }

        fun getProcessInfo(context: Context): String {
            val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val processInfo = manager.runningAppProcesses?.find {
                it.pid == Process.myPid()
            }

            return processInfo?.let {
                "进程名: ${it.processName}\n" +
                "PID: ${it.pid}\n" +
                "重要性: ${getImportanceDescription(it.importance)}\n" +
                "LRU: ${it.lru}"
            } ?: "无法获取进程信息"
        }

        private fun getImportanceDescription(importance: Int): String {
            return when (importance) {
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND -> "前台进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND_SERVICE -> "前台服务进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_TOP_SLEEPING -> "顶层休眠进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_VISIBLE -> "可见进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_PERCEPTIBLE -> "可感知进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_CANT_SAVE_STATE -> "无法保存状态进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_SERVICE -> "服务进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_CACHED -> "缓存进程"
                ActivityManager.RunningAppProcessInfo.IMPORTANCE_GONE -> "已销毁进程"
                else -> "未知($importance)"
            }
        }
    }
}
```

### 10.2 内存管理优化

```kotlin
class MemoryOptimizationManager {

    companion object {
        fun optimizeMemoryUsage(context: Context) {
            // 1. 清理不必要的缓存
            clearUnnecessaryCache()

            // 2. 释放大对象
            releaseLargeObjects()

            // 3. 触发垃圾回收
            System.gc()

            // 4. 监控内存使用
            monitorMemoryUsage(context)
        }

        private fun clearUnnecessaryCache() {
            // 清理图片缓存
            // 清理网络缓存
            // 清理临时文件
            Log.d("MemoryOptimization", "清理缓存完成")
        }

        private fun releaseLargeObjects() {
            // 释放大型对象引用
            // 清理静态变量
            Log.d("MemoryOptimization", "释放大对象完成")
        }

        fun monitorMemoryUsage(context: Context) {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val availableMemory = maxMemory - usedMemory

            Log.d("MemoryMonitor", """
                系统可用内存: ${memoryInfo.availMem / 1024 / 1024}MB
                系统总内存: ${memoryInfo.totalMem / 1024 / 1024}MB
                系统内存不足: ${memoryInfo.lowMemory}
                应用已用内存: ${usedMemory / 1024 / 1024}MB
                应用最大内存: ${maxMemory / 1024 / 1024}MB
                应用可用内存: ${availableMemory / 1024 / 1024}MB
            """.trimIndent())

            // 如果内存使用过高，执行清理
            if (usedMemory > maxMemory * 0.8) {
                Log.w("MemoryMonitor", "内存使用过高，执行清理")
                clearUnnecessaryCache()
                System.gc()
            }
        }

        fun registerMemoryPressureCallback(context: Context) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
                (context as? Application)?.registerComponentCallbacks(object : ComponentCallbacks2 {
                    override fun onConfigurationChanged(newConfig: Configuration) {}

                    override fun onLowMemory() {
                        Log.w("MemoryPressure", "系统内存不足")
                        optimizeMemoryUsage(context)
                    }

                    override fun onTrimMemory(level: Int) {
                        Log.w("MemoryPressure", "内存压力等级: $level")
                        when (level) {
                            ComponentCallbacks2.TRIM_MEMORY_RUNNING_MODERATE,
                            ComponentCallbacks2.TRIM_MEMORY_RUNNING_LOW,
                            ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL -> {
                                // 应用在前台运行，但系统内存不足
                                optimizeMemoryUsage(context)
                            }
                            ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN -> {
                                // 应用UI不可见
                                clearUnnecessaryCache()
                            }
                            ComponentCallbacks2.TRIM_MEMORY_BACKGROUND,
                            ComponentCallbacks2.TRIM_MEMORY_MODERATE,
                            ComponentCallbacks2.TRIM_MEMORY_COMPLETE -> {
                                // 应用在后台，内存压力大
                                optimizeMemoryUsage(context)
                            }
                        }
                    }
                })
            }
        }
    }
}
```

---

## 11. 网络保活

通过网络连接保持应用活跃状态。

### 11.1 心跳包机制

```kotlin
class HeartbeatManager private constructor() {

    private var heartbeatTimer: Timer? = null
    private var isHeartbeatRunning = false
    private val heartbeatInterval = 30 * 1000L // 30秒

    companion object {
        @Volatile
        private var INSTANCE: HeartbeatManager? = null

        fun getInstance(): HeartbeatManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: HeartbeatManager().also { INSTANCE = it }
            }
        }
    }

    fun startHeartbeat() {
        if (isHeartbeatRunning) return

        isHeartbeatRunning = true
        heartbeatTimer = Timer("HeartbeatTimer", true)

        heartbeatTimer?.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                sendHeartbeat()
            }
        }, 0, heartbeatInterval)

        Log.d("HeartbeatManager", "心跳包机制已启动")
    }

    fun stopHeartbeat() {
        if (!isHeartbeatRunning) return

        isHeartbeatRunning = false
        heartbeatTimer?.cancel()
        heartbeatTimer = null

        Log.d("HeartbeatManager", "心跳包机制已停止")
    }

    private fun sendHeartbeat() {
        Thread {
            try {
                // 发送心跳包到服务器
                val response = performHeartbeatRequest()
                handleHeartbeatResponse(response)

                Log.d("HeartbeatManager", "心跳包发送成功")
            } catch (e: Exception) {
                Log.e("HeartbeatManager", "心跳包发送失败", e)
                handleHeartbeatFailure()
            }
        }.start()
    }

    private fun performHeartbeatRequest(): String {
        // 实现具体的网络请求
        // 这里可以使用OkHttp、Retrofit等网络库

        val url = "https://your-server.com/heartbeat"
        val connection = java.net.URL(url).openConnection()
        connection.connectTimeout = 5000
        connection.readTimeout = 5000

        return connection.getInputStream().bufferedReader().use { it.readText() }
    }

    private fun handleHeartbeatResponse(response: String) {
        // 处理服务器响应
        // 可以根据响应内容执行相应的保活策略
    }

    private fun handleHeartbeatFailure() {
        // 处理心跳失败
        // 可以尝试重连或调整心跳间隔
    }
}
```

### 11.2 长连接保活

```kotlin
class LongConnectionManager {

    private var webSocket: WebSocket? = null
    private var okHttpClient: OkHttpClient? = null
    private var isConnected = false
    private var reconnectAttempts = 0
    private val maxReconnectAttempts = 5
    private val reconnectDelay = 5000L

    fun connect() {
        if (isConnected) return

        okHttpClient = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()

        val request = Request.Builder()
            .url("wss://your-server.com/websocket")
            .build()

        webSocket = okHttpClient?.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                super.onOpen(webSocket, response)
                isConnected = true
                reconnectAttempts = 0
                Log.d("LongConnection", "WebSocket连接成功")

                // 发送认证信息
                sendAuthMessage()
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                super.onMessage(webSocket, text)
                Log.d("LongConnection", "收到消息: $text")
                handleMessage(text)
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                super.onClosing(webSocket, code, reason)
                Log.d("LongConnection", "WebSocket正在关闭: $code - $reason")
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                super.onClosed(webSocket, code, reason)
                isConnected = false
                Log.d("LongConnection", "WebSocket已关闭: $code - $reason")

                // 尝试重连
                scheduleReconnect()
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                super.onFailure(webSocket, t, response)
                isConnected = false
                Log.e("LongConnection", "WebSocket连接失败", t)

                // 尝试重连
                scheduleReconnect()
            }
        })
    }

    fun disconnect() {
        isConnected = false
        webSocket?.close(1000, "正常关闭")
        webSocket = null
        okHttpClient = null
    }

    fun sendMessage(message: String) {
        if (isConnected) {
            webSocket?.send(message)
        } else {
            Log.w("LongConnection", "连接未建立，无法发送消息")
        }
    }

    private fun sendAuthMessage() {
        val authMessage = """
            {
                "type": "auth",
                "token": "your-auth-token",
                "timestamp": ${System.currentTimeMillis()}
            }
        """.trimIndent()
        sendMessage(authMessage)
    }

    private fun handleMessage(message: String) {
        try {
            // 解析消息并执行相应操作
            // 可以根据消息类型执行不同的保活策略

            when {
                message.contains("ping") -> {
                    sendMessage("pong")
                }
                message.contains("keep_alive") -> {
                    // 执行保活逻辑
                    performKeepAliveAction()
                }
            }
        } catch (e: Exception) {
            Log.e("LongConnection", "处理消息失败", e)
        }
    }

    private fun scheduleReconnect() {
        if (reconnectAttempts >= maxReconnectAttempts) {
            Log.w("LongConnection", "重连次数已达上限，停止重连")
            return
        }

        reconnectAttempts++
        Log.d("LongConnection", "将在${reconnectDelay}ms后进行第${reconnectAttempts}次重连")

        Handler(Looper.getMainLooper()).postDelayed({
            connect()
        }, reconnectDelay)
    }

    private fun performKeepAliveAction() {
        // 执行保活相关操作
        Log.d("LongConnection", "执行保活操作")
    }
}
```

---

## 12. 最佳实践和注意事项

### 12.1 保活策略选择

```kotlin
class KeepAliveStrategySelector {

    companion object {
        fun selectOptimalStrategy(context: Context): List<KeepAliveStrategy> {
            val strategies = mutableListOf<KeepAliveStrategy>()

            // 1. 根据Android版本选择策略
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                    // Android 8.0+：推荐使用WorkManager和前台服务
                    strategies.add(KeepAliveStrategy.WORK_MANAGER)
                    strategies.add(KeepAliveStrategy.FOREGROUND_SERVICE)
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    // Android 6.0+：可以使用AlarmManager和JobScheduler
                    strategies.add(KeepAliveStrategy.ALARM_MANAGER)
                    strategies.add(KeepAliveStrategy.JOB_SCHEDULER)
                    strategies.add(KeepAliveStrategy.FOREGROUND_SERVICE)
                }
                else -> {
                    // 旧版本：可以使用更多策略
                    strategies.add(KeepAliveStrategy.DUAL_PROCESS)
                    strategies.add(KeepAliveStrategy.BROADCAST_RECEIVER)
                    strategies.add(KeepAliveStrategy.ALARM_MANAGER)
                }
            }

            // 2. 根据厂商选择策略
            if (ManufacturerHelper.needsWhitelistGuidance()) {
                strategies.add(KeepAliveStrategy.WHITELIST_GUIDE)
            }

            // 3. 根据应用类型选择策略
            if (isSystemApp(context)) {
                strategies.add(KeepAliveStrategy.SYSTEM_LEVEL)
            }

            // 4. 根据用户权限选择策略
            if (hasIgnoreBatteryOptimization(context)) {
                strategies.add(KeepAliveStrategy.IGNORE_BATTERY_OPTIMIZATION)
            }

            return strategies
        }

        private fun isSystemApp(context: Context): Boolean {
            val packageManager = context.packageManager
            val applicationInfo = packageManager.getApplicationInfo(context.packageName, 0)
            return (applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
        }

        private fun hasIgnoreBatteryOptimization(context: Context): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
                powerManager.isIgnoringBatteryOptimizations(context.packageName)
            } else {
                true
            }
        }
    }

    enum class KeepAliveStrategy {
        WORK_MANAGER,
        FOREGROUND_SERVICE,
        ALARM_MANAGER,
        JOB_SCHEDULER,
        DUAL_PROCESS,
        BROADCAST_RECEIVER,
        ACCOUNT_SYNC,
        WHITELIST_GUIDE,
        SYSTEM_LEVEL,
        IGNORE_BATTERY_OPTIMIZATION
    }
}
```

### 12.2 权限申请管理

```kotlin
class PermissionManager(private val context: Context) {

    fun requestKeepAlivePermissions(activity: Activity) {
        // 1. 请求忽略电池优化
        requestIgnoreBatteryOptimization(activity)

        // 2. 请求自启动权限（厂商相关）
        requestAutoStartPermission()

        // 3. 请求后台运行权限
        requestBackgroundPermission()

        // 4. 请求通知权限
        requestNotificationPermission()
    }

    private fun requestIgnoreBatteryOptimization(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            if (!powerManager.isIgnoringBatteryOptimizations(context.packageName)) {
                val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                    data = Uri.parse("package:${context.packageName}")
                }
                try {
                    activity.startActivity(intent)
                } catch (e: Exception) {
                    Log.e("PermissionManager", "请求忽略电池优化失败", e)
                }
            }
        }
    }

    private fun requestAutoStartPermission() {
        // 根据厂商跳转到相应的自启动设置页面
        WhitelistGuideManager(context).showWhitelistGuide()
    }

    private fun requestBackgroundPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ 后台位置权限
            // 这里可以根据需要请求相应的后台权限
        }
    }

    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 通知权限
            if (ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.POST_NOTIFICATIONS
                ) != PackageManager.PERMISSION_GRANTED) {

                if (context is Activity) {
                    ActivityCompat.requestPermissions(
                        context,
                        arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                        1001
                    )
                }
            }
        }
    }

    fun checkAllPermissions(): PermissionStatus {
        val status = PermissionStatus()

        // 检查电池优化
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            status.ignoreBatteryOptimization = powerManager.isIgnoringBatteryOptimizations(context.packageName)
        } else {
            status.ignoreBatteryOptimization = true
        }

        // 检查通知权限
        status.notificationPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }

        return status
    }

    data class PermissionStatus(
        var ignoreBatteryOptimization: Boolean = false,
        var notificationPermission: Boolean = false,
        var autoStartPermission: Boolean = false
    )
}
```

### 12.3 性能监控

```kotlin
class KeepAliveMonitor {

    private val metrics = mutableMapOf<String, Any>()

    fun startMonitoring(context: Context) {
        // 监控服务状态
        monitorServiceStatus(context)

        // 监控内存使用
        monitorMemoryUsage(context)

        // 监控电池使用
        monitorBatteryUsage(context)

        // 监控网络状态
        monitorNetworkStatus(context)
    }

    private fun monitorServiceStatus(context: Context) {
        Thread {
            while (true) {
                try {
                    val isKeepAliveServiceRunning = isServiceRunning(context, KeepAliveService::class.java)
                    val isMainProcessServiceRunning = isServiceRunning(context, MainProcessService::class.java)
                    val isGuardProcessServiceRunning = isServiceRunning(context, GuardProcessService::class.java)

                    metrics["keep_alive_service"] = isKeepAliveServiceRunning
                    metrics["main_process_service"] = isMainProcessServiceRunning
                    metrics["guard_process_service"] = isGuardProcessServiceRunning

                    Log.d("KeepAliveMonitor", """
                        服务状态监控:
                        保活服务: $isKeepAliveServiceRunning
                        主进程服务: $isMainProcessServiceRunning
                        守护进程服务: $isGuardProcessServiceRunning
                    """.trimIndent())

                    Thread.sleep(10000) // 10秒检查一次
                } catch (e: InterruptedException) {
                    break
                } catch (e: Exception) {
                    Log.e("KeepAliveMonitor", "服务状态监控异常", e)
                }
            }
        }.start()
    }

    private fun monitorMemoryUsage(context: Context) {
        Thread {
            while (true) {
                try {
                    val runtime = Runtime.getRuntime()
                    val usedMemory = runtime.totalMemory() - runtime.freeMemory()
                    val maxMemory = runtime.maxMemory()
                    val memoryUsagePercent = (usedMemory.toDouble() / maxMemory * 100).toInt()

                    metrics["memory_usage_percent"] = memoryUsagePercent
                    metrics["used_memory_mb"] = usedMemory / 1024 / 1024
                    metrics["max_memory_mb"] = maxMemory / 1024 / 1024

                    if (memoryUsagePercent > 80) {
                        Log.w("KeepAliveMonitor", "内存使用率过高: $memoryUsagePercent%")
                        // 触发内存优化
                        MemoryOptimizationManager.optimizeMemoryUsage(context)
                    }

                    Thread.sleep(30000) // 30秒检查一次
                } catch (e: InterruptedException) {
                    break
                } catch (e: Exception) {
                    Log.e("KeepAliveMonitor", "内存监控异常", e)
                }
            }
        }.start()
    }

    private fun monitorBatteryUsage(context: Context) {
        val batteryReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
                val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
                val batteryPercent = (level.toFloat() / scale * 100).toInt()

                metrics["battery_percent"] = batteryPercent

                Log.d("KeepAliveMonitor", "电池电量: $batteryPercent%")

                // 低电量时调整保活策略
                if (batteryPercent < 20) {
                    adjustKeepAliveStrategy(context, true)
                } else if (batteryPercent > 50) {
                    adjustKeepAliveStrategy(context, false)
                }
            }
        }

        val filter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        context.registerReceiver(batteryReceiver, filter)
    }

    private fun monitorNetworkStatus(context: Context) {
        val networkReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val isConnected = isNetworkAvailable(context)
                metrics["network_connected"] = isConnected

                Log.d("KeepAliveMonitor", "网络状态: ${if (isConnected) "已连接" else "未连接"}")

                if (isConnected) {
                    // 网络恢复时重启网络相关的保活机制
                    HeartbeatManager.getInstance().startHeartbeat()
                } else {
                    // 网络断开时停止网络相关的保活机制
                    HeartbeatManager.getInstance().stopHeartbeat()
                }
            }
        }

        val filter = IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
        context.registerReceiver(networkReceiver, filter)
    }

    private fun adjustKeepAliveStrategy(context: Context, isLowBattery: Boolean) {
        if (isLowBattery) {
            // 低电量时采用节能策略
            Log.d("KeepAliveMonitor", "低电量模式，调整保活策略")

            // 降低心跳频率
            HeartbeatManager.getInstance().stopHeartbeat()

            // 减少WorkManager任务频率
            // 可以重新配置WorkManager的约束条件

        } else {
            // 正常电量时恢复正常策略
            Log.d("KeepAliveMonitor", "正常电量，恢复保活策略")

            // 恢复心跳
            HeartbeatManager.getInstance().startHeartbeat()

            // 恢复正常的保活机制
            KeepAliveManager.getInstance(context).startKeepAlive()
        }
    }

    private fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        return manager.getRunningServices(Integer.MAX_VALUE).any {
            it.service.className == serviceClass.name
        }
    }

    private fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            connectivityManager.activeNetworkInfo?.isConnected == true
        }
    }

    fun getMetrics(): Map<String, Any> {
        return metrics.toMap()
    }
}
```

---

## 13. 完整的保活方案示例

### 13.1 Application初始化

```kotlin
class KeepAliveApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        // 初始化保活系统
        initKeepAliveSystem()

        // 注册内存压力回调
        MemoryOptimizationManager.registerMemoryPressureCallback(this)

        // 启动监控
        KeepAliveMonitor().startMonitoring(this)
    }

    private fun initKeepAliveSystem() {
        // 1. 启动保活管理器
        KeepAliveManager.getInstance(this).startKeepAlive()

        // 2. 注册系统广播
        BroadcastManager(this).registerBroadcasts()

        // 3. 启动心跳机制
        HeartbeatManager.getInstance().startHeartbeat()

        // 4. 设置进程优先级
        ProcessPriorityManager.setHighPriority()

        Log.d("KeepAliveApplication", "保活系统初始化完成")
    }
}
```

### 13.2 MainActivity集成

```kotlin
class MainActivity : AppCompatActivity() {

    private lateinit var permissionManager: PermissionManager
    private lateinit var whitelistGuideManager: WhitelistGuideManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        permissionManager = PermissionManager(this)
        whitelistGuideManager = WhitelistGuideManager(this)

        // 检查并请求权限
        checkAndRequestPermissions()

        // 显示白名单引导
        showWhitelistGuideIfNeeded()

        // 启动保活服务
        startKeepAliveServices()
    }

    private fun checkAndRequestPermissions() {
        val status = permissionManager.checkAllPermissions()

        if (!status.ignoreBatteryOptimization || !status.notificationPermission) {
            permissionManager.requestKeepAlivePermissions(this)
        }
    }

    private fun showWhitelistGuideIfNeeded() {
        if (ManufacturerHelper.needsWhitelistGuidance()) {
            whitelistGuideManager.showWhitelistGuide()
        }
    }

    private fun startKeepAliveServices() {
        // 启动前台服务
        val intent = Intent(this, KeepAliveService::class.java)
        startForegroundService(intent)

        // 启动保活管理器
        KeepAliveManager.getInstance(this).startKeepAlive()
    }

    override fun onDestroy() {
        super.onDestroy()

        // 注意：不要在这里停止保活机制
        // 保活机制应该在应用真正退出时才停止
    }
}
```

---

## 14. 总结和建议

### 14.1 保活技术总结

1. **WorkManager**: 现代化的后台任务处理方案，推荐优先使用
2. **AlarmManager**: 定时任务保活，适用于周期性任务
3. **前台服务**: 最可靠的保活方式，但会显示通知
4. **双进程守护**: 传统保活方法，在新版本Android中效果有限
5. **广播拉活**: 通过系统广播实现拉活，受系统限制较多
6. **账户同步**: 相对隐蔽的保活方式，但实现复杂
7. **厂商适配**: 针对不同厂商的特殊处理，提高保活成功率

### 14.2 最佳实践建议

1. **合理选择策略**: 根据Android版本和厂商选择合适的保活策略
2. **用户体验优先**: 不要过度保活，影响用户体验和设备性能
3. **权限申请**: 主动引导用户授予必要的权限
4. **性能监控**: 实时监控保活效果和资源使用情况
5. **优雅降级**: 在保活失败时有备用方案
6. **遵守规范**: 遵循Google和各厂商的开发规范

### 14.3 注意事项

1. **避免过度保活**: 不要为了保活而牺牲用户体验
2. **内存管理**: 注意内存使用，避免因内存不足被系统杀死
3. **电量优化**: 在低电量时调整保活策略
4. **版本适配**: 针对不同Android版本采用不同策略
5. **厂商测试**: 在主流厂商设备上充分测试保活效果

通过合理组合使用这些保活技术，可以在大多数情况下实现应用的稳定运行。但需要注意的是，随着Android系统的不断更新和厂商定制的变化，保活策略也需要持续优化和调整。
