<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.keepalive">

    <!-- 基础权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    
    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- 开机启动权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    
    <!-- 电池优化权限 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    
    <!-- 通知权限 (Android 13+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    
    <!-- 系统级权限 (需要系统签名) -->
    <uses-permission 
        android:name="android.permission.WRITE_SECURE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    
    <!-- 账户同步权限 -->
    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.READ_SYNC_STATS" />

    <application
        android:name=".KeepAliveApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/AppTheme"
        android:persistent="true"
        tools:ignore="AllowBackup">

        <!-- 主Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 透明Activity用于拉活 -->
        <activity
            android:name=".TransparentActivity"
            android:exported="false"
            android:theme="@style/TransparentTheme"
            android:excludeFromRecents="true"
            android:taskAffinity=""
            android:finishOnTaskLaunch="true" />

        <!-- 保活服务 -->
        <service
            android:name=".service.KeepAliveService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- 主进程服务 -->
        <service
            android:name=".service.MainProcessService"
            android:enabled="true"
            android:exported="false" />

        <!-- 守护进程服务 (运行在独立进程) -->
        <service
            android:name=".service.GuardProcessService"
            android:enabled="true"
            android:exported="false"
            android:process=":guard" />

        <!-- WorkManager相关 -->
        <provider
            android:name="androidx.work.impl.WorkManagerInitializer"
            android:authorities="${applicationId}.workmanager-init"
            android:exported="false"
            tools:node="remove" />

        <!-- 自定义WorkManager初始化 -->
        <provider
            android:name=".provider.CustomWorkManagerInitializer"
            android:authorities="${applicationId}.custom-workmanager-init"
            android:exported="false" />

        <!-- 保活广播接收器 -->
        <receiver
            android:name=".receiver.KeepAliveAlarmReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter android:priority="1000">
                <action android:name="com.example.keepalive.KEEP_ALIVE" />
                <action android:name="com.example.keepalive.RESTART_SERVICE" />
            </intent-filter>
        </receiver>

        <!-- 系统广播接收器 -->
        <receiver
            android:name=".receiver.SystemBroadcastReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter android:priority="1000">
                <!-- 开机启动 -->
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
                
                <!-- 应用替换 -->
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
                
                <!-- 屏幕状态 (需要动态注册) -->
                <!-- <action android:name="android.intent.action.SCREEN_ON" /> -->
                <!-- <action android:name="android.intent.action.SCREEN_OFF" /> -->
                <!-- <action android:name="android.intent.action.USER_PRESENT" /> -->
                
                <!-- 网络状态 -->
                <action android:name="android.net.conn.CONNECTIVITY_ACTION" />
                
                <!-- 电源状态 -->
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>

        <!-- 账户同步相关 -->
        
        <!-- 认证器服务 -->
        <service
            android:name=".sync.KeepAliveAuthenticatorService"
            android:exported="false">
            <intent-filter>
                <action android:name="android.accounts.AccountAuthenticator" />
            </intent-filter>
            <meta-data
                android:name="android.accounts.AccountAuthenticator"
                android:resource="@xml/authenticator" />
        </service>

        <!-- 同步适配器服务 -->
        <service
            android:name=".sync.KeepAliveSyncService"
            android:exported="false">
            <intent-filter>
                <action android:name="android.content.SyncAdapter" />
            </intent-filter>
            <meta-data
                android:name="android.content.SyncAdapter"
                android:resource="@xml/sync_adapter" />
        </service>

        <!-- 内容提供者 -->
        <provider
            android:name=".provider.KeepAliveContentProvider"
            android:authorities="com.example.keepalive.provider"
            android:exported="false"
            android:syncable="true" />

        <!-- JobScheduler服务 (API 21+) -->
        <service
            android:name=".job.KeepAliveJobService"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:exported="false" />

    </application>

</manifest>
